<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="expires" content="0" />
  <meta http-equiv="pragma" content="no-cache" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="robots" content="noindex, nofollow">
  <meta name="apple-mobile-web-app-title" content="Eq360 Stg" />
  <title>Equalindo 360 Staging</title>
  <meta name="google" content="notranslate" />
  <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#FFFFFF" />

  <link rel="apple-touch-icon" sizes="72x72" href="icons/icon-72x72.png">
  <link rel="apple-touch-icon" sizes="96x96" href="icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="128x128" href="icons/icon-128x128.png">
  <link rel="apple-touch-icon" sizes="144x144" href="icons/icon-144x144.png">
  <link rel="apple-touch-icon" sizes="152x152" href="icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="192x192" href="icons/icon-192x192.png">
  <link rel="apple-touch-icon" sizes="384x384" href="icons/icon-384x384.png">
  <link rel="apple-touch-icon" sizes="512x512" href="icons/icon-512x512.png">

  <meta name="description" content="Equalindo Group ERP Platform" />
  <!-- Facebook Meta Tags -->
  <meta property="og:title" content="Equalindo 360 Staging" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://equalindo360.com" />
  <meta property="og:description" content="Equalindo Group ERP Platform" />
  <meta property="og:image" content="https://cdn.equalindo360.com/icons/icon-72x72.png" />
  <!-- Twitter Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://equalindo360.com/" />
  <meta name="twitter:title" content="Equalindo 360 Staging" />
  <meta name="twitter:description" content="Equalindo Group ERP Platform" />
  <meta name="twitter:image" content="https://cdn.equalindo360.com/icons/icon-72x72.png" />


</head>

<body class="notranslate">
  <div id="root"></div>
  <script>
    // Capture install prompt early and defer showing it until UI is ready
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      // Save for later usage by the UI
      window.promptEvent = e;
      // Notify the app layer that a deferred prompt is available
      window.dispatchEvent(new CustomEvent('pwa:beforeinstallprompt', { detail: { event: e } }));
    });
  </script>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>
