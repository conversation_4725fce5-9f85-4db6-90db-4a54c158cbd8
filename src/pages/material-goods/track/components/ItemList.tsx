import { useEffect, useMemo } from 'react'
import { FormControl, InputLabel, ListItemText, ListSubheader, MenuItem, Select, Typography } from '@mui/material'
import Card from '@mui/material/Card'

import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import { tableColumns } from '../config/table'
import { useItemTrack } from '../context/ItemTrackContext'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'

const ItemList = () => {
  const router = useRouter()
  const { ownSiteList, groupedSiteList } = useAuth()
  const {
    itemListResponse: { items: itemList, totalItems, totalPages },
    itemsParams: { page, search, categoryId, siteId, startDate, endDate, limit, isInStock },
    setItemsParams,
    setPartialItemsParams,
    categoryList
  } = useItemTrack()
  const warehouseGroupedSiteList = groupedSiteList
    .map(group => ({
      ...group,
      sites: group.sites
    }))
    .filter(group => group.sites?.length > 0)

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: itemList,
    columns: tableColumns(
      {
        showDetail: id => {
          router.push(`/mg/track/${id}`)
        }
      },
      siteId ?? 'all'
    ),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const handleCategoryChange = (value: string) => {
    setItemsParams(prev => ({
      ...prev,
      page: 1,
      categoryId: value || undefined
    }))
  }

  useEffect(() => {
    setItemsParams(current => ({
      ...current,
      limit: 10,
      page: 1,
      siteId: 'all'
    }))
  }, [])

  useEffect(() => {
    if (siteId === 'all') {
      setItemsParams(current => ({
        ...current,
        siteIds: ownSiteList.map(site => site.id).join(',')
      }))
    }
  }, [siteId])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setItemsParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari Barang'
            className='is-full sm:is-auto'
          />
          <FormControl size='small' className='w-[220px] max-sm:is-full'>
            <InputLabel id='role-select'>Pilih Lokasi</InputLabel>
            <Select
              key={siteId}
              fullWidth
              id='select-siteId'
              value={siteId}
              onChange={e => setPartialItemsParams('siteId', e.target.value)}
              label='Pilih Lokasi'
              size='small'
              labelId='siteId-select'
              inputProps={{ placeholder: 'Pilih Lokasi' }}
              defaultValue='all'
            >
              <MenuItem value='all'>Semua Lokasi</MenuItem>
              {warehouseGroupedSiteList.map(group => {
                let children = []
                children.push(
                  <ListSubheader
                    className='bg-green-50 text-primary font-semibold'
                    key={group.projectId ?? 'no_project'}
                  >
                    {group.project?.name || 'Tanpa Proyek'}
                  </ListSubheader>
                )
                group.sites.forEach(site => {
                  children.push(
                    <MenuItem key={site.id} value={site.id}>
                      <ListItemText primary={site.name} />
                    </MenuItem>
                  )
                })
                return children
              })}
            </Select>
          </FormControl>
          <FormControl size='small' className='w-full lg:w-[220px]'>
            <InputLabel shrink id='track-department-select'>
              Kategori
            </InputLabel>
            <Select
              labelId='track-department-select'
              value={categoryId ?? ''}
              label='Kategori'
              size='small'
              displayEmpty
              onChange={event => handleCategoryChange(event.target.value as string)}
            >
              <MenuItem value=''>Semua Kategori</MenuItem>
              {(categoryList ?? []).map(category => (
                <MenuItem key={category.id} value={category.id}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* key=siteId supaya reconcile perubahan prop untuk default filter lokasi*/}
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography> Belum ada Barang</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua stok barang di gudang akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setItemsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialItemsParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setItemsParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialItemsParams('page', pageIndex)}
      />
    </Card>
  )
}

export default ItemList
