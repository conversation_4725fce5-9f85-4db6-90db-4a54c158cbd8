import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  FormControl,
  InputLabel,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  Table as MuiTable,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  TableBody,
  Chip
} from '@mui/material'
import Card from '@mui/material/Card'

import {
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type Row
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import { tableDetailColumns } from '../config/table'
import { useItemTrack } from '../context/ItemTrackContext'
import DocDetail from './DocDetail'
import type { RequestedItem } from '@/types/mrTypes'
import { prStatusOptions } from '@/pages/purchase-requisition/list/config/utils'
import { statusChipColor } from '@/pages/purchase-requisition/detail/table'
import { poStatusOptions } from '@/pages/purchase-order/config/options'

const ItemDetailList = () => {
  const router = useRouter()
  const { ownSiteList, departmentList, groupedSiteList } = useAuth()
  const {
    itemDetailListResponse: itemListResponse,
    itemsParams,
    setItemsParams,
    setPartialItemsParams,
    fetchItemsDetailLoading: fetchItemsLoading
  } = useItemTrack()

  const {
    items: itemList = [],
    totalItems = 0,
    totalPages = 0
  } = itemListResponse ?? {
    items: [],
    totalItems: 0,
    totalPages: 0
  }

  const { search = '', limit = 10, page = 1, siteId, departmentId } = itemsParams

  const allSiteIds = useMemo(() => (ownSiteList ?? []).map(site => site.id), [ownSiteList])
  const selectedSiteValue = siteId ?? 'all'

  const expandedRowRenderer = useCallback((row: Row<RequestedItem>) => {
    const {
      purchaseRequisitionItems = [],
      purchaseOrderItemPortions = [],
      incomingMaterialItemPortions = []
    } = row.original

    const renderEmptyMessage = (message: string) => (
      <Typography className='px-4 pb-4 text-sm text-gray-500'>{message}</Typography>
    )

    return (
      <div className='bg-gray-50 p-4'>
        <div className='flex flex-col gap-4'>
          <div className='rounded border border-gray-200 bg-white shadow-sm'>
            <Typography variant='h5' className='px-4 pt-4 font-semibold'>
              PR Terbuat
            </Typography>
            <div className='overflow-x-auto px-4 pb-4 pt-2'>
              {purchaseRequisitionItems.length ? (
                <MuiTable size='small'>
                  <TableHead>
                    <TableRow className='bg-gray-100'>
                      <TableCell>No. PR</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Qty</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {purchaseRequisitionItems.map(item => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Typography
                            color='primary'
                            className='cursor-pointer'
                            onClick={() => router.push(`/pr/list/${item.purchaseRequisition?.id}`)}
                          >
                            {item.purchaseRequisition?.number ?? '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={
                              prStatusOptions.find(option => option.value === item.purchaseRequisition?.status)?.label
                            }
                            color={statusChipColor[item.purchaseRequisition?.status].color}
                            variant='tonal'
                            size='small'
                          />
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {item.quantity} {item.quantityUnit}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </MuiTable>
              ) : (
                renderEmptyMessage('Belum ada PR terbuat')
              )}
            </div>
          </div>

          <div className='rounded border border-gray-200 bg-white shadow-sm'>
            <Typography variant='h5' className='px-4 pt-4 font-semibold'>
              PO Terbuat
            </Typography>
            <div className='overflow-x-auto px-4 pb-4 pt-2'>
              {purchaseOrderItemPortions.length ? (
                <MuiTable size='small'>
                  <TableHead>
                    <TableRow className='bg-gray-100'>
                      <TableCell>No. PO</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Qty</TableCell>
                      <TableCell>Qty Diterima</TableCell>
                      <TableCell>Qty Belum Diterima</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {purchaseOrderItemPortions.map(item => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Typography
                            color='primary'
                            className='cursor-pointer'
                            onClick={() => router.push(`/po/list/${item.purchaseOrderItem?.purchaseOrder?.id}`)}
                          >
                            {item.purchaseOrderItem?.purchaseOrder?.number ?? '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={
                              poStatusOptions.find(
                                option => option.value === item.purchaseOrderItem?.purchaseOrder?.status
                              )?.label
                            }
                            color={statusChipColor[item.purchaseOrderItem?.purchaseOrder?.status].color}
                            variant='tonal'
                            size='small'
                          />
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {item.quantity} {item.purchaseOrderItem?.quantityUnit}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {item.receivedQuantity} {item.purchaseOrderItem?.quantityUnit}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {item.remainingQuantity} {item.purchaseOrderItem?.quantityUnit}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </MuiTable>
              ) : (
                renderEmptyMessage('Belum ada PO terbuat')
              )}
            </div>
          </div>

          <div className='rounded border border-gray-200 bg-white shadow-sm'>
            <Typography variant='h5' className='px-4 pt-4 font-semibold'>
              Penerimaan Barang
            </Typography>
            <div className='overflow-x-auto px-4 pb-4 pt-2'>
              {incomingMaterialItemPortions.length ? (
                <MuiTable size='small'>
                  <TableHead>
                    <TableRow className='bg-gray-100'>
                      <TableCell>No. Penerimaan</TableCell>
                      <TableCell>No. DO/SJ/SBKB</TableCell>
                      <TableCell>Qty</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {incomingMaterialItemPortions.map(item => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Typography
                            color='primary'
                            className='cursor-pointer'
                            onClick={() => {
                              if (!!item.incomingMaterialItem?.incomingMaterial?.purchaseOrderId) {
                                router.push(`/mg/in/${item.incomingMaterialItem?.incomingMaterial?.purchaseOrderId}`)
                              }
                            }}
                          >
                            {item.incomingMaterialItem?.incomingMaterial?.number ?? '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {item.incomingMaterialItem?.incomingMaterial?.deliveryNoteNumber ?? '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {item.smallQuantity} {item.incomingMaterialItem?.quantityUnit}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </MuiTable>
              ) : (
                renderEmptyMessage('Belum ada penerimaan barang')
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }, [])

  const table = useReactTable({
    data: itemList,
    columns: tableDetailColumns({
      showDetail(id, docType) {
        router.push(`/mr/list/${id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    getExpandedRowModel: getExpandedRowModel(),
    getRowCanExpand: () => true
  })

  // useEffect(() => {
  //   table.setPageIndex(Math.max(0, (page ?? 1) - 1))
  // }, [page, table])

  // useEffect(() => {
  //   table.setPageSize(limit ?? 10)
  // }, [limit, table])

  const handleSearchChange = (value: string | number) => {
    setItemsParams(prev => ({
      ...prev,
      search: value ? String(value) : undefined,
      page: 1
    }))
  }

  const handleSiteChange = (value: string) => {
    if (value === 'all') {
      const ids = allSiteIds.join(',')
      setItemsParams(prev => ({
        ...prev,
        page: 1,
        siteId: undefined,
        siteIds: ids || undefined
      }))
      return
    }

    setItemsParams(prev => ({
      ...prev,
      page: 1,
      siteId: value,
      siteIds: value
    }))
  }

  const handleDepartmentChange = (value: string) => {
    setItemsParams(prev => ({
      ...prev,
      page: 1,
      departmentId: value || undefined
    }))
  }

  return (
    <Card>
      <div className='flex flex-col gap-4 p-5 pb-4 lg:flex-row lg:items-center lg:justify-between'>
        <div className='flex w-full flex-col gap-4 lg:flex-row lg:items-center'>
          <DebouncedInput
            value={search ?? ''}
            onChange={handleSearchChange}
            placeholder='Cari Nomor MR'
            className='w-full lg:w-[260px]'
            size='small'
            disabled={fetchItemsLoading && itemList.length === 0}
          />
          <FormControl size='small' className='w-full lg:w-[220px]'>
            <InputLabel id='track-location-select'>Lokasi</InputLabel>
            <Select
              labelId='track-location-select'
              value={selectedSiteValue}
              label='Lokasi'
              size='small'
              onChange={event => handleSiteChange(event.target.value as string)}
            >
              <MenuItem value='all'>Semua Lokasi</MenuItem>
              {groupedSiteList.map(group => {
                let children = []
                children.push(
                  <ListSubheader
                    className='bg-green-50 text-primary font-semibold'
                    key={group.projectId ?? 'no_project'}
                  >
                    {group.project?.name || 'Tanpa Proyek'}
                  </ListSubheader>
                )
                group.sites.forEach(site => {
                  children.push(
                    <MenuItem key={site.id} value={site.id}>
                      <ListItemText primary={site.name} />
                    </MenuItem>
                  )
                })
                return children
              })}
            </Select>
          </FormControl>
          <FormControl size='small' className='w-full lg:w-[220px]'>
            <InputLabel id='track-department-select'>Departemen</InputLabel>
            <Select
              labelId='track-department-select'
              value={departmentId ?? ''}
              label='Departemen'
              size='small'
              onChange={event => handleDepartmentChange(event.target.value as string)}
            >
              <MenuItem value=''>Semua Departemen</MenuItem>
              {(departmentList ?? []).map(department => (
                <MenuItem key={department.id} value={department.id}>
                  {department.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>
      </div>
      <Table
        table={table}
        headerColor='green'
        containerClassName='px-5 pb-5'
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='h-60 text-center'>
            <Typography>Belum ada Dokumen Terbuat</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua dokumen terbuat dari barang ini akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setItemsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialItemsParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setItemsParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => {
          setPartialItemsParams('page', pageIndex)
        }}
        renderRowSubComponent={expandedRowRenderer}
      />
    </Card>
  )
}

export default ItemDetailList
