import { useEffect, useMemo, useState } from 'react'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Table as MuiTable,
  TableCell,
  TableHead,
  TableRow
} from '@mui/material'
import { RequestedItem } from '@/types/mrTypes'

const DocDetail = ({ requestedItem, index }: { requestedItem: RequestedItem; index: number }) => {
  const [expanded, setExpanded] = useState(false)

  return (
    <Accordion key={requestedItem?.id} expanded={expanded} onChange={() => setExpanded(current => !current)}>
      <AccordionSummary
        expandIcon={<i className='ri-arrow-down-s-line size-6' />}
        aria-controls={`${name}-panel-content`}
        id={`${name}-panel-header`}
        className='bg-green-50'
      >
        <TableRow>
          <TableCell>1902310391203</TableCell>
          <TableCell>1902310391203</TableCell>
          <TableCell>1902310391203</TableCell>
          <TableCell>1902310391203</TableCell>
        </TableRow>
      </AccordionSummary>
      <AccordionDetails className='flex flex-col gap-2'></AccordionDetails>
    </Accordion>
  )
}

export default DocDetail
