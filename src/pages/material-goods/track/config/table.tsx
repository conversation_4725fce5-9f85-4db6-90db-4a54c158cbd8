import { Typography, IconButton, colors, Chip } from '@mui/material'
import { RequestedItem } from '@/types/mrTypes'
import { createColumnHelper } from '@tanstack/react-table'

import {
  getIncomingMaterialNumbers,
  getMaterialRequestStatusLabel,
  getPurchaseOrderNumbers,
  getPurchaseRequisitionNumbers
} from './utils'
import { ItemType } from '@/types/companyTypes'
import { DEFAULT_CATEGORY } from '@/data/default/category'
import { mrStatusOptions } from '@/pages/material-request/list/config/utils'
import { statusChipColor } from '@/pages/purchase-requisition/detail/table'

type RequestedItemTypeWithAction = RequestedItem & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

type RowActionDetailType = {
  showDetail: (id: string, docType: string) => void
}

// Column Definitions
const columnDetailHelper = createColumnHelper<RequestedItemTypeWithAction>()

const renderDocumentSummary = (numbers: string[]) => {
  if (!numbers?.length) {
    return <Typography>-</Typography>
  }

  return (
    <div className='flex flex-col gap-0.5 justify-start h-full'>
      {numbers.map(number => (
        <Typography key={number} color='primary'>
          {number}
        </Typography>
      ))}
    </div>
  )
}

export const tableDetailColumns = (rowAction: RowActionDetailType) => [
  columnDetailHelper.display({
    id: 'expander',
    header: '',
    cell: ({ row }) => {
      const canExpand = row.getCanExpand()
      return (
        <IconButton
          size='small'
          onClick={event => {
            event.stopPropagation()
            row.toggleExpanded()
          }}
          disabled={!canExpand}
        >
          <i className={`ri-arrow-${row.getIsExpanded() ? 'up' : 'down'}-s-line`} />
        </IconButton>
      )
    },
    enableSorting: false,
    meta: {
      headerAlign: 'center'
    }
  }),
  columnDetailHelper.accessor('materialRequest.number', {
    header: 'No. MR',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.materialRequest?.id ?? '', 'mr')}
      >
        {row.original.materialRequest?.number}
      </Typography>
    )
  }),
  columnDetailHelper.accessor('materialRequest.status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={mrStatusOptions.find(option => option.value === row.original.materialRequest?.status)?.label}
        color={statusChipColor[row.original.materialRequest?.status].color}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnDetailHelper.accessor('materialRequest.unit', {
    header: 'Unit',
    cell: ({ row }) =>
      row.original.materialRequest?.unit ? (
        <div className='flex flex-col'>
          <Typography>{row.original.materialRequest?.unit?.brandName}</Typography>
          <Typography variant='caption'>{row.original.materialRequest?.unit?.number}</Typography>
        </div>
      ) : (
        '-'
      )
  }),
  columnDetailHelper.accessor('materialRequest.site', {
    header: 'Lokasi',
    cell: ({ row }) => <Typography>{row.original.materialRequest?.site?.name}</Typography>
  }),
  columnDetailHelper.accessor('materialRequest.department.name', {
    header: 'Departemen',
    cell: ({ row }) => <Typography>{row.original.materialRequest?.department?.name}</Typography>
  }),
  columnDetailHelper.accessor('materialRequest.createdByUser.fullName', {
    header: 'Dibuat Oleh',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.materialRequest?.createdByUser?.fullName}</Typography>
        <Typography variant='caption'>{row.original.materialRequest?.createdByUser?.title}</Typography>
      </div>
    )
  })
]

type ItemTypeWithAction = ItemType & {
  action?: string
}

const columnHelper = createColumnHelper<ItemTypeWithAction>()

export const tableColumns = (rowAction: RowActionType, siteId?: string) => [
  columnHelper.accessor('number', {
    header: 'Kode Barang',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: 'Nama Item',
    cell: ({ row }) => <Typography>{row.original.name}</Typography>
  }),
  columnHelper.accessor('brandName', {
    header: 'Merk Item',
    cell: ({ row }) => <Typography>{row.original.brandName}</Typography>
  }),
  columnHelper.accessor('category.name', {
    header: 'Kategori Item',
    cell: ({ row }) => <Typography>{row.original.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
  }),
  columnHelper.accessor('stock', {
    header: 'Stok',
    cell: ({ row }) => (
      <Typography>
        {siteId === 'all'
          ? row.original.stock
          : row.original.stocks.find(stock => stock.siteId === siteId)?.totalStock ?? 0}{' '}
        {row.original.smallUnit}
      </Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Lihat Dokumen',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
