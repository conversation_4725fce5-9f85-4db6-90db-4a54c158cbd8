import { RequestedItem } from '@/types/mrTypes'

const unique = (values: Array<string | undefined>) => {
  return Array.from(new Set(values.filter(Boolean) as string[]))
}

export const getMaterialRequestStatusLabel = (status?: string) => {
  if (!status) {
    return '-'
  }

  return status
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

export const getMaterialRequestNumber = (item: RequestedItem) => item.materialRequest?.number

export const getPurchaseRequisitionNumbers = (item: RequestedItem) => {
  return unique(item.purchaseRequisitionItems?.map(pr => pr.purchaseRequisition?.number))
}

export const getPurchaseOrderNumbers = (item: RequestedItem) => {
  return unique(
    item.purchaseOrderItemPortions?.map(poPortion => poPortion.purchaseOrderItem?.purchaseOrder?.number)
  )
}

export const getIncomingMaterialNumbers = (item: RequestedItem) => {
  return unique(
    item.incomingMaterialItemPortions?.map(portion => portion.incomingMaterialItem?.incomingMaterial?.number)
  )
}
