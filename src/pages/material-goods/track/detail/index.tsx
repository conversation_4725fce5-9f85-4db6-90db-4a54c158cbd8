import Grid from '@mui/material/Grid'
import { Breadcrumbs, Typography } from '@mui/material'
import { useItemTrack } from '../context/ItemTrackContext'
import ItemDetailList from '../components/ItemDetailList'
import { Link } from 'react-router-dom'

const ItemTrackDetailPage = () => {
  const { itemData } = useItemTrack()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='/mg/track' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Lacak Barang</Typography>
          </Link>
          <Typography>Dokumen Terbuat</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>{itemData?.name ?? ''}</Typography>
            <Typography>Kode <PERSON>ang {itemData?.number ?? ''}</Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <ItemDetailList />
      </Grid>
    </Grid>
  )
}

export default ItemTrackDetailPage
