// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import { Button, FormControl, FormHelperText, Grid, MenuItem, Select, TextField, Typography } from '@mui/material'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import { tableColumns } from './config/table'
import Table from '@/components/table'
import { useMg } from '../../../context/MgContext'
import { ImPayload, ImPoItemType, ImType } from '@/types/mgTypes'
import { useAddIncomingMaterial } from '@/api/services/mg/mutation'
import { useUploadDocument } from '@/api/services/file/mutation'
import { useFilePicker } from 'use-file-picker'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import LoadingButton from '@mui/lab/LoadingButton'
import { format, formatISO, toDate } from 'date-fns'
import { useAuth } from '@/contexts/AuthContext'
import { WarehouseItemType } from '@/types/appTypes'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { useAddStockMovement } from '@/api/services/stock-movement/mutation'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { useQuery } from '@tanstack/react-query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useRouter } from '@/routes/hooks'
import { ApiResponse } from '@/types/api'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker.tsx'
import { CompanySiteType } from '@/types/companyTypes'
import Permission from '@/core/components/Permission'

const MgInTableCard = () => {
  const { ownSiteList, userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const router = useRouter()
  const { poData, fetchPoData, fetchPoList, fetchImList, fetchLogList } = useMg()
  const { control, handleSubmit, setValue, reset, getValues } = useForm<ImPayload>()

  const [poItemList, setPoItemList] = useState([])

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadDocument()
  const { mutate: addImMutate, isLoading: addImLoading } = useAddIncomingMaterial()
  const { mutate: createSmMutate, isLoading: createSmLoading } = useAddStockMovement()

  const [{ open: editItemOpen, selectedItem }, setEditItemModalState] = useState({
    open: false,
    selectedItem: {} as WarehouseItemType
  })

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: poData?.siteId
  })

  const itemsWatch = useWatch({
    control,
    name: 'items',
    defaultValue: []
  })

  // Check if any row has quantity > remaining quantity to show excess column
  const showExcessColumn = useMemo(() => {
    return poItemList.some((item, index) => {
      const remainingQty = item.isLargeUnit ? item.remainingQuantity / item.largeUnitQuantity : item.remainingQuantity
      const qtyValue = itemsWatch?.[index]?.quantity ?? 0
      return qtyValue > remainingQty
    })
  }, [poItemList, itemsWatch])

  const isCrossSite = siteIdWatch !== poData?.siteId

  const isLoading = uploadLoading || addImLoading

  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/*'],
    readAs: 'DataURL'
  })

  const { data: smApproverList } = useQuery({
    enabled: isCrossSite && !!siteIdWatch && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, DefaultApprovalScope.StockMovement, siteIdWatch, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope: DefaultApprovalScope.StockMovement,
        siteId: siteIdWatch,
        departmentId: 'null'
        // departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSubmitHandler: SubmitHandler<ImPayload> = (inputValues: ImPayload) => {
    const activeItems = (inputValues.items.filter(item => (item.quantity ?? 0) > 0) ?? ([] as ImPoItemType[])).map(
      item => {
        const remainingQty = item.isLargeUnit ? item.remainingQuantity / item.largeUnitQuantity : item.remainingQuantity
        if (remainingQty < item.quantity) {
          return {
            ...item,
            excessQuantity: item.quantity - remainingQty
          }
        }
        return item
      }
    )
    if (activeItems.length > 0) {
      if (activeItems?.some(item => (item.excessQuantity ?? 0) > 0)) {
        setConfirmState({
          open: true,
          title: 'Barang Over Supply',
          confirmText: 'Ya, Lanjutkan',
          content: `Apakah kamu yakin menerima barang dengan jumlah yang melebihi jumlah barang di PO?`,
          onConfirm: () => {
            confirmCrossSite(inputValues, activeItems)
          }
        })
      } else {
        confirmCrossSite(inputValues, activeItems)
      }
    }
  }

  const confirmCrossSite = (inputValues: ImPayload, activeItems: ImPoItemType[]) => {
    if (isCrossSite) {
      setConfirmState({
        open: true,
        title: 'Tambah Pindah Barang',
        confirmText: 'Tambah Pindah Barang',
        content: `Apakah kamu yakin akan menambahkan permintaan pindah barang dari ${ownSiteList?.find(site => site.id === inputValues.siteId)?.name} ke ${poData?.site?.name}? Action ini tidak bisa diubah`,
        onConfirm: () => {
          if ((smApproverList?.length ?? 0) <= 0) {
            toast.error('Default Approval Pindah Barang belum tersedia. Silahkan hubungi admin terlebih dahulu.')
            return
          }
          handleAddIncomingMaterial(inputValues, activeItems, imResponse => {
            createSmMutate(
              {
                originSiteId: inputValues.siteId,
                destinationSiteId: poData?.siteId,
                items: activeItems.map(item => {
                  const poItem = poItemList.find(poItem => String(poItem.id) === String(item.purchaseOrderItemId))
                  return {
                    itemId: poItem?.itemId,
                    quantity: item.quantity,
                    quantityUnit: item.quantityUnit,
                    largeUnitQuantity: item.largeUnitQuantity,
                    note: item.note
                  }
                }),
                note: imResponse?.data?.note ?? '',
                purchaseOrderId: poData?.id ?? '',
                incomingMaterialId: imResponse?.data?.id,
                approvals:
                  smApproverList?.map(approver => ({
                    userId: approver.user?.id
                  })) ?? []
              },
              {
                onSuccess: response => {
                  toast.success('Pindah Barang berhasil disimpan')
                  router.replace(`/mg/sm/list/${response.data?.id}`)
                }
              }
            )
          })
        }
      })
    } else {
      submitIncomingMaterial(inputValues, activeItems)
    }
  }

  const submitIncomingMaterial = (inputValues: ImPayload, activeItems: ImPoItemType[]) => {
    setConfirmState({
      open: true,
      title: 'Konfirmasi dan Perbarui Stok',
      confirmText: 'Konfirmasi',
      content: `Pastikan quantity yang kamu masukkan sudah sesuai dengan yang kamu terima. Apakah kamu yakin akan mengkonfirmasi penerimaan barang dan memperbarui stok? Action ini tidak dapat diubah`,
      onConfirm: () => {
        handleAddIncomingMaterial(inputValues, activeItems)
      }
    })
  }

  const handleAddIncomingMaterial = (
    inputValues: ImPayload,
    activeItems: ImPoItemType[],
    onSuccess?: (response: ApiResponse<ImType>) => void
  ) => {
    if (filesContent?.length > 0) {
      uploadMutate(
        {
          fieldName: `incoming_material_file_${format(new Date(), 'yyyyMMddHHmmss')}`,
          file: filesContent?.[0]?.content,
          scope: 'public-document',
          fileName: filesContent?.[0]?.name
        },
        {
          onSuccess: response => {
            addImMutate(
              {
                ...inputValues,
                purchaseOrderId: poData?.id ?? '',
                deliveryNoteUploadId: response.data?.id,
                items: activeItems
              },
              {
                onSuccess: response => {
                  if (isCrossSite) {
                    setTimeout(() => {
                      onSuccess?.(response)
                    }, 500)
                  } else {
                    fetchPoData()
                    fetchImList()
                    fetchLogList()
                    fetchPoList()
                    toast.success('Barang berhasil diterima dan stok berhasil diperbarui')
                    setTimeout(() => {
                      resetValues()
                    }, 300)
                  }
                }
              }
            )
          }
        }
      )
    } else {
      addImMutate(
        {
          ...inputValues,
          purchaseOrderId: poData?.id ?? '',
          items: activeItems
        },
        {
          onSuccess: response => {
            if (isCrossSite) {
              setTimeout(() => {
                onSuccess?.(response)
              }, 500)
            } else {
              fetchPoData()
              fetchImList()
              fetchLogList()
              fetchPoList()
              toast.success('Barang berhasil diterima dan stok berhasil diperbarui')
              setTimeout(() => {
                resetValues()
              }, 300)
            }
          }
        }
      )
    }
  }

  const resetValues = () => {
    const poItems = poData?.items?.filter(item => item.remainingQuantity > 0) ?? []
    clear()
    reset({
      ...getValues(),
      items:
        poItems?.map(item => ({
          purchaseOrderItemId: item.id,
          quantityUnit: item.quantityUnit,
          largeUnitQuantity: item.largeUnitQuantity,
          quantity: 0
        })) ?? [],
      deliveryNoteNumber: '',
      deliveryNoteUploadId: null,
      note: '',
      purchaseOrderId: poData?.id ?? ''
    })
  }

  const table = useReactTable({
    data: poItemList,
    columns: useMemo(
      () =>
        tableColumns(
          control,
          {
            editItem: itemData => {
              setEditItemModalState({
                open: true,
                selectedItem: itemData
              })
            }
          },
          showExcessColumn
        ),
      [control, showExcessColumn]
    ),
    initialState: {
      pagination: {
        pageSize: 100
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    const poItems = poData?.items?.filter(item => item.remainingQuantity > 0) ?? []
    setValue(
      'items',
      poItems?.map(item => ({
        purchaseOrderItemId: item.id,
        quantityUnit: item.quantityUnit,
        largeUnitQuantity: item.largeUnitQuantity,
        remainingQuantity: item.remainingQuantity,
        isLargeUnit: item.isLargeUnit
      })) ?? []
    )
    setPoItemList(poData?.closedAt ? [] : poItems ?? [])
  }, [poData])

  useEffect(() => {
    if (poData && ownSiteList) {
      const ownWarehouseSites = ownSiteList?.filter(site => site.type === CompanySiteType.WAREHOUSE) ?? []
      const defaultSiteId = (ownWarehouseSites?.find(site => site.id === poData?.siteId) ?? ownWarehouseSites?.[0])?.id
      setValue('siteId', defaultSiteId ?? '')
    }
  }, [ownSiteList, poData])

  return (
    <>
      <Card>
        <CardHeader
          title='Barang Masuk'
          action={
            <Permission permission={['incoming-material.create']}>
              <LoadingButton
                startIcon={<></>}
                loadingPosition='start'
                loading={isLoading || uploadLoading || addImLoading || createSmLoading}
                onClick={handleSubmit(onSubmitHandler, e => console.error({ e }))}
                variant='contained'
                disabled={!!poData?.closedAt}
                color={poData?.closedAt ? 'secondary' : 'primary'}
                className='px-10'
              >
                {isCrossSite ? 'Pindahkan Barang' : 'Perbarui Stok'}
              </LoadingButton>
            </Permission>
          }
        />
        <CardContent>
          <Table
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography>Barang sudah diterima</Typography>
                <Typography className='text-sm text-gray-400'>
                  Semua barang dalam PO ini sudah diterima dan ditambahkan ke stok
                </Typography>
              </td>
            }
          />
          {poItemList?.length > 0 && (itemsWatch?.filter(item => (item.quantity ?? 0) > 0)?.length ?? 0) <= 0 && (
            <FormHelperText error>Barang wajib dipilih</FormHelperText>
          )}
          {!poData?.closedAt ? (
            <div className='flex flex-col gap-6 mt-4 md:flex-row'>
              <div className='flex flex-col gap-4 flex-1'>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <Typography className='font-semibold'>Nomor DO/SJ/SBKB</Typography>
                      <Controller
                        control={control}
                        rules={{ required: 'Nomor DO/SJ/SBKB wajib diisi' }}
                        name='deliveryNoteNumber'
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <TextField
                              {...field}
                              variant='outlined'
                              size='small'
                              placeholder='Masukkan nomor DO/SJ/SBKB'
                              disabled={isLoading}
                              fullWidth
                              {...(error?.message && { error: true, helperText: error?.message })}
                            />
                          )
                        }}
                      />
                    </div>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <div className='flex flex-col gap-2'>
                      <Typography className='font-semibold'>Barang Diterima di</Typography>
                      <Controller
                        control={control}
                        name='siteId'
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControl size='small'>
                              <Select
                                key={value}
                                id='select-site'
                                onChange={e => onChange(e.target.value)}
                                size='small'
                                labelId='site-select'
                                inputProps={{ placeholder: 'Pilih Lokasi' }}
                                value={value}
                              >
                                {ownSiteList
                                  .filter(site => site.type === CompanySiteType.WAREHOUSE)
                                  .map(site => (
                                    <MenuItem key={site.id} value={site.id}>
                                      {site.name}
                                    </MenuItem>
                                  ))}
                              </Select>
                            </FormControl>
                          )
                        }}
                      />
                    </div>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <div className='flex flex-col gap-2'>
                      <Typography className='font-semibold'>Tanggal Shipping</Typography>
                      <Controller
                        control={control}
                        rules={{ required: 'Tanggal Shipping wajib diisi' }}
                        name='shipDate'
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <AppReactDatepicker
                              boxProps={{ className: 'is-full' }}
                              selected={field.value ? toDate(field.value) : undefined}
                              onChange={(date: Date) => field.onChange(formatISO(date))}
                              dateFormat='dd MMM yyyy'
                              customInput={
                                <TextField
                                  fullWidth
                                  className='flex-1'
                                  size='small'
                                  InputProps={{
                                    readOnly: true,
                                    placeholder: 'Pilih Tanggal'
                                  }}
                                  {...(error?.message && { error: true, helperText: error?.message })}
                                />
                              }
                            />
                          )
                        }}
                      />
                    </div>
                  </Grid>
                </Grid>
                <Grid container spacing={2}>
                  {/* <Grid item xs={12} md={6}>
                    <div className='flex flex-col gap-2'>
                      <Typography className='font-semibold'>Tanggal Invoice (Opsional)</Typography>
                      <Controller
                        control={control}
                        rules={{ required: false }}
                        name='invoiceDate'
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <AppReactDatepicker
                              boxProps={{ className: 'is-full' }}
                              selected={field.value ? toDate(field.value) : undefined}
                              onChange={(date: Date) => field.onChange(formatISO(date))}
                              dateFormat='dd MMM yyyy'
                              customInput={
                                <TextField
                                  fullWidth
                                  className='flex-1'
                                  size='small'
                                  InputProps={{
                                    readOnly: true,
                                    placeholder: 'Pilih Tanggal'
                                  }}
                                  {...(error?.message && { error: true, helperText: error?.message })}
                                />
                              }
                            />
                          )
                        }}
                      />
                    </div>
                  </Grid> */}
                </Grid>
              </div>
              <div className='flex flex-col gap-2 flex-1'>
                <div className='flex flex-col gap-2 flex-1'>
                  <Typography className='font-semibold'>Unggah DO/SJ/SBKB (Opsional)</Typography>
                  <div className='flex items-center gap-4'>
                    <TextField
                      key={JSON.stringify(filesContent)}
                      size='small'
                      fullWidth
                      value={filesContent?.[0]?.name}
                      placeholder='Tidak ada file dipilih'
                      aria-readonly
                      className='flex-1'
                    />
                    <Permission permission={['incoming-material.create']}>
                      <Button variant='contained' onClick={() => openFilePicker()} disabled={isLoading}>
                        Pilih File
                      </Button>
                    </Permission>
                  </div>
                </div>
                <Controller
                  control={control}
                  name='note'
                  render={({ field }) => {
                    return (
                      <TextField
                        {...field}
                        label='Catatan'
                        variant='outlined'
                        size='small'
                        disabled={isLoading}
                        multiline
                        rows={3}
                      />
                    )
                  }}
                />
              </div>
            </div>
          ) : null}
        </CardContent>
      </Card>
      {editItemOpen && (
        <AddWarehouseItemDialog
          open={editItemOpen}
          setOpen={open => {
            setEditItemModalState(current => ({
              open,
              selectedItem: !open ? {} : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          currentItem={selectedItem}
          onSubmit={itemData => {
            setPoItemList(current => {
              const tempCurrent = [...current]
              const itemIndex = tempCurrent.findIndex(item => item.itemId === selectedItem.itemId)
              tempCurrent[itemIndex] = itemData
              return tempCurrent
            })
            const tempFormItems = [...getValues('items')]
            const itemIndex = tempFormItems.findIndex(item => item.purchaseOrderItemId === selectedItem.id)
            tempFormItems[itemIndex] = {
              ...tempFormItems[itemIndex],
              itemId: itemData.itemId
            }
            setValue('items', tempFormItems)
            setEditItemModalState({
              open: false,
              selectedItem: {} as WarehouseItemType
            })
          }}
          editItemOnly
          withoutUnit
          siteId={poData?.siteId}
        />
      )}
    </>
  )
}

export default MgInTableCard
