import { StatusChipColorType, WarehouseItemType } from '@/types/appTypes'
import { Button, FormControl, FormHelperText, InputLabel, MenuItem, Select, TextField, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { ImPayload, MgStatus } from '@/types/mgTypes'
import { Control, Controller, useWatch } from 'react-hook-form'
import NumberField from '@/components/numeric/NumberField'
import { isNullOrUndefined } from '@/utils/helper'
import Permission from '@/core/components/Permission'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [MgStatus.RECEIVED]: { color: 'success' },
  [MgStatus.PENDING]: { color: 'secondary' }
}

type RowActionType = {
  editItem: (itemData: WarehouseItemType) => void
}

type WarehouseItemTypeWithAction = WarehouseItemType & {
  action?: string
}

// Column Definitions
const columnHelper = createColumnHelper<WarehouseItemTypeWithAction>()

export const tableColumns = (control: Control<ImPayload, any>, rowAction: RowActionType) => [
  columnHelper.accessor('item.vendorNumber', {
    header: 'Kode Eksternal',
    cell: ({ row }) => <Typography>{row.original.item?.vendorNumber}</Typography>
  }),
  columnHelper.accessor('item.name', {
    header: 'Nama Item',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.item?.name}</Typography>
        <Typography variant='caption'>
          {row.original.item?.brandName} - {row.original.item?.number}
        </Typography>
      </div>
    )
  }),
  columnHelper.accessor('remainingQuantity', {
    header: 'QTY',
    cell: ({ row }) => {
      const remainingQty = row.original.isLargeUnit
        ? row.original.remainingQuantity / row.original.largeUnitQuantity
        : row.original.remainingQuantity
      return (
        <Typography>
          {parseFloat(remainingQty.toFixed(2))} {row.original.quantityUnit}
        </Typography>
      )
    }
  }),
  columnHelper.accessor('quantity', {
    header: 'QTY DITERIMA',
    cell: ({ row }) => {
      return (
        <Controller
          control={control}
          name={`items.${row.index}.quantity`}
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              size='small'
              className='w-24'
              InputProps={{
                endAdornment: row.original.quantityUnit,
                inputComponent: NumberField as any,
                inputProps: {
                  isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined
                }
              }}
              {...(error && { error: true, helperText: 'Wajib diisi.' })}
            />
          )}
        />
      )
    }
  }),
  columnHelper.accessor('excessIsBonus', {
    id: 'excessIsBonus',
    header: 'STATUS KELEBIHAN',
    cell: ({ row }) => {
      const remainingQty = row.original.isLargeUnit
        ? row.original.remainingQuantity / row.original.largeUnitQuantity
        : row.original.remainingQuantity
      const qtyWatch = useWatch({
        control,
        name: `items.${row.index}.quantity`,
        defaultValue: 0
      })
      return qtyWatch > remainingQty ? (
        <Controller
          control={control}
          rules={{ validate: value => (isNullOrUndefined(value) ? 'Wajib dipilih.' : true) }}
          name={`items.${row.index}.excessIsBonus`}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <FormControl fullWidth size='small' className='w-44'>
              <InputLabel>Status Kelebihan</InputLabel>
              <Select
                onChange={e => onChange(e.target.value === 1)}
                label='Status Kelebihan'
                value={!isNullOrUndefined(value) ? (value ? 1 : 2) : undefined}
                className='bg-white'
                error={Boolean(error)}
                size='small'
                fullWidth
              >
                <MenuItem key={2} value={2}>
                  Tidak Bonus
                </MenuItem>
                <MenuItem key={1} value={1}>
                  Bonus
                </MenuItem>
              </Select>
              {error && <FormHelperText error>Wajib dipilih.</FormHelperText>}
            </FormControl>
          )}
        />
      ) : (
        <Typography>-</Typography>
      )
    }
  }),
  columnHelper.accessor('excessQuantity', {
    id: 'excessQuantity',
    header: 'QTY KELEBIHAN',
    cell: ({ row }) => {
      const remainingQty = row.original.isLargeUnit
        ? row.original.remainingQuantity / row.original.largeUnitQuantity
        : row.original.remainingQuantity
      const qtyWatch = useWatch({
        control,
        name: `items.${row.index}.quantity`,
        defaultValue: 0
      })
      return qtyWatch > remainingQty ? (
        <div className='flex gap-2'>
          <Controller
            control={control}
            rules={{ validate: value => (isNullOrUndefined(value) ? 'Wajib diisi.' : true) }}
            name={`items.${row.index}.excessQuantity`}
            render={({ field, fieldState: { error } }) => (
              <TextField
                {...field}
                size='small'
                className='w-20'
                placeholder='0'
                InputProps={{
                  inputComponent: NumberField as any,
                  inputProps: {
                    isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined
                  }
                }}
                {...(error && { error: true, helperText: 'Wajib diisi.' })}
              />
            )}
          />
          <Controller
            control={control}
            name={`items.${row.index}.excessQuantityUnit`}
            rules={{ validate: value => (isNullOrUndefined(value) ? 'Wajib dipilih.' : true) }}
            render={({ field, fieldState: { error } }) => (
              <FormControl size='small' className='w-20'>
                <Select {...field} size='small' className='bg-white' error={Boolean(error)} displayEmpty>
                  <MenuItem value={row.original.item?.smallUnit}>{row.original.item?.smallUnit}</MenuItem>
                  <MenuItem value={row.original.item?.largeUnit}>{row.original.item?.largeUnit}</MenuItem>
                </Select>
                {error && <FormHelperText error>Wajib dipilih.</FormHelperText>}
              </FormControl>
            )}
          />
        </div>
      ) : (
        <Typography>-</Typography>
      )
    }
  }),
  columnHelper.accessor('note', {
    header: 'Catatan',
    cell: ({ row }) => (
      <Controller
        control={control}
        name={`items.${row.index}.note`}
        render={({ field, fieldState: { error } }) => (
          <TextField
            {...field}
            size='small'
            placeholder='Masukkan catatan'
            {...(error && { error: true, helperText: 'Wajib diisi.' })}
          />
        )}
      />
    )
  }),
  columnHelper.accessor('item', {
    header: 'Action',
    cell: ({ row }) => (
      <Permission permission={['incoming-material.create']}>
        <Button variant='contained' size='small' onClick={() => rowAction.editItem(row.original)}>
          Ubah Barang
        </Button>
      </Permission>
    ),
    enableSorting: false
  })
]
