import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { MrPriority, mrPriorityOptions } from '@/pages/material-request/create/config/enum'
import { StockMovementType } from '../../config/type'

type SmTypeWithAction = StockMovementType & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<SmTypeWithAction>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. Pindah Barang',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('deliveryNotesCount', {
    header: 'Status Surat Jalan',
    cell: ({ row }) => {
      const deliveryNotesCount = row.original.deliveryNotesCount ?? 0
      return (
        <Chip
          label={deliveryNotesCount > 0 ? `${deliveryNotesCount} SJ dibuat` : 'SJ belum dibuat'}
          color={deliveryNotesCount > 0 ? 'success' : 'default'}
          variant='tonal'
          size='small'
        />
      )
    }
  }),
  // columnHelper.accessor('itemsCount', {
  //   header: 'Item',
  //   cell: ({ row }) => <Typography>{row.original.itemsCount} Item</Typography>
  // }),
  columnHelper.accessor('originSite', {
    header: 'Gudang Asal',
    cell: ({ row }) => <Typography>{row.original.originSite?.name}</Typography>
  }),
  columnHelper.accessor('destinationSite', {
    header: 'Gudang Tujuan',
    cell: ({ row }) => <Typography>{row.original.destinationSite?.name}</Typography>
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => <Typography>{row.original.department?.name}</Typography>
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = mrPriorityOptions.find(option => option.value === String(row.original.priority ?? MrPriority.P4))
      return (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal Dibuat',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
