import { ItemType } from '@/types/companyTypes'
import {
  Autocomplete,
  Card,
  CardContent,
  CircularProgress,
  debounce,
  Divider,
  FormHelperText,
  Grid,
  TextField,
  Typography
} from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { segmentTable } from '@/pages/repair-and-maintenance/wp/new-wp/config/table'
import Table from '@/components/table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { CreateWpDto } from '@/types/wpTypes'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import RnMQueryMethods from '@/api/services/rnm/query'
import { WorkOrderType, WoSegmentItem, WoSegmentType, WoStatus } from '@/types/woTypes'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { WO_QUERY_DETAIL_KEY, WO_QUERY_LIST_KEY, WO_SEGMENT_KEY } from '@/api/services/rnm/service'
import DetilSegmentWo from '@/components/dialogs/detil-segment-wo'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { useLocation } from 'react-router-dom'
import { WoPartSwapDtoType } from '@/types/partSwapTypes'
import { useSrForm } from '../../context/SrFormContext'
import { SrRmOPayload } from '@/types/srTypes'

const SegmentCard = () => {
  const locations = useLocation()
  const searchUrl = new URLSearchParams(locations.search)
  const { selectedWo, setSelectedWo, setActiveSegment, setSelectedWoId } = useSrForm()
  const [woQuerySearch, setWoQuerySearch] = useState<string>('')
  const [selectedSegmentId, setSelectedSegmentId] = useState<string | null>(null)
  const [itemState, setItemState] = useState<{ state: boolean; item: WoSegmentItem | null }>({
    state: false,
    item: null
  })
  const { control, getValues, setValue } = useFormContext<SrRmOPayload>()

  const woSegmentId = useWatch({ control, name: 'workOrderSegmentId' })

  const {
    data: woList,
    remove: removeItemList,
    isFetching: fetchItemsLoading
  } = useQuery({
    enabled: !!woQuerySearch,
    queryKey: [WO_QUERY_LIST_KEY, woQuerySearch],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        search: woQuerySearch,
        status: WoStatus.ACTIVE
      })
      return res.items
    }
  })

  const { data: woData } = useQuery({
    enabled: !!searchUrl.get('wo') || !!getValues('workOrderId'),
    queryKey: [WO_QUERY_DETAIL_KEY, searchUrl.get('wo'), getValues('workOrderId')],
    queryFn: () => RnMQueryMethods.getWoDetail(searchUrl.get('wo') || getValues('workOrderId'))
  })

  const { data: woSegments } = useQuery({
    enabled: !!selectedWo?.id,
    queryKey: ['WO_SEGMENT_LIST', selectedWo?.id],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoSegments(selectedWo?.id, { limit: Number.MAX_SAFE_INTEGER })
      return res
    },
    placeholderData: defaultListData as ListResponse<WoSegmentType>
  })

  useQuery({
    enabled: !!woSegmentId && !!selectedWo?.id,
    queryKey: [WO_SEGMENT_KEY, woSegmentId, selectedWo],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoSegment(selectedWo?.id, woSegmentId)
      if (res) setActiveSegment(res)
      return res
    }
  })

  const tableOptions = useMemo(
    () => ({
      data: woSegments.items ?? [],
      columns: segmentTable({
        onDetail: (id: string) => setSelectedSegmentId(id),
        onSelectedId: (id: string) => {
          if (woSegmentId === id) {
            setActiveSegment(null)
            setValue('workOrderSegmentId', null)
          } else {
            setValue('workOrderSegmentId', id)
          }
        },
        single: true,
        woId: woSegmentId
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [woSegments, woSegmentId, getValues()]
  )

  const tableSegment = useReactTable<any>(tableOptions)

  const handleItemClick = useCallback((item: WoSegmentItem) => {
    setItemState(curr => ({ ...curr, state: true, item }))
  }, [])

  useEffect(() => {
    if (woData) {
      setSelectedWo(woData)
    }
  }, [woData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Pilih Segment Pengerjaan</Typography>
          </div>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='workOrderSegmentId'
                render={({}) => (
                  <Autocomplete
                    filterOptions={x => x}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setWoQuerySearch(newValue)
                      }
                    }, 700)}
                    options={woList ?? []}
                    freeSolo={!woQuerySearch}
                    fullWidth
                    value={selectedWo}
                    onChange={(e, newValue: WorkOrderType) => {
                      setValue('workOrderId', newValue.id)
                      setSelectedWoId(newValue.id)
                      setSelectedWo(newValue)
                      removeItemList()
                    }}
                    noOptionsText='Barang tidak ditemukan'
                    loading={fetchItemsLoading}
                    renderInput={params => (
                      <TextField
                        {...params}
                        label=''
                        placeholder='Cari nomor WO'
                        variant='outlined'
                        fullWidth
                        InputProps={{
                          ...params.InputProps,
                          startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                          endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                      />
                    )}
                    getOptionLabel={(option: WorkOrderType) =>
                      `${option.number} | ${option.createdAt ? formatDate(option.createdAt, 'dd MMM yyyy', { locale: id }) : ''}`
                    }
                    renderOption={(props, option) => {
                      const { key, ...optionProps } = props
                      return (
                        <li key={key} {...optionProps}>
                          <Typography>
                            {option.number} |{' '}
                            {option.createdAt ? formatDate(option.createdAt, 'dd MMM yyyy', { locale: id }) : ''}
                          </Typography>
                        </li>
                      )
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-2'>
                <Typography className='font-semibold'>No. WO: {selectedWo?.number}</Typography>
                <Typography>
                  {selectedWo?.createdAt
                    ? formatDate(new Date(selectedWo.createdAt), 'dd MMM yyyy', { locale: id })
                    : '-'}
                </Typography>
              </div>
            </Grid>
            <Grid item xs={12}>
              <Divider />
            </Grid>
            <Grid item xs={12}>
              <Typography>Pilih Segment</Typography>
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='workOrderSegmentId'
                render={({ fieldState: { error } }) => (
                  <>
                    <div className='shadow-xs rounded-[8px]'>
                      <Table
                        table={tableSegment}
                        emptyLabel={
                          <td colSpan={tableSegment.getVisibleFlatColumns().length} className='text-center h-60'>
                            <Typography>Belum ada Segment</Typography>
                            <Typography className='text-sm text-gray-400'>
                              Tambahkan segment yang ingin dimasukkan dalam Work Process ini
                            </Typography>
                          </td>
                        }
                        disablePagination
                      />
                    </div>
                    {!!error && (
                      <FormHelperText className='text-base' error>
                        Wajib dipilih
                      </FormHelperText>
                    )}
                  </>
                )}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      {!!selectedSegmentId && (
        <DetilSegmentWo
          onItemClick={handleItemClick}
          open={!!selectedSegmentId}
          setOpen={bool => (!bool ? setSelectedSegmentId(null) : null)}
          selectedSegmentId={selectedSegmentId}
          woId={selectedWo?.id}
        />
      )}
      {!!itemState.state && (
        <AddWarehouseItemDialog
          withoutUnit
          currentItem={{ ...itemState.item, itemId: itemState.item.item.id }}
          viewOnly
          open={itemState.state}
          setOpen={open => setItemState(curr => ({ ...curr, state: open, ...(!open && { item: null }) }))}
          onSubmit={() => {}}
        />
      )}
    </>
  )
}

export default memo(SegmentCard)
