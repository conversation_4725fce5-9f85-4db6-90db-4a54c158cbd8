// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrumbs, Button, Chip, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useSr } from '../context/SrContext'
import { ServiceRequisition, ServiceRequisitionStatus } from '@/types/serviceRequisitionsTypes'
import { useReadSrApproval } from '@/api/services/service-requisitions/mutation'
import { statusChipColor } from '../list/config/table'
import { srStatusOptions } from '../list/config/utils'

// Components
import ItemListCard from './components/ItemListCard'
import ApprovalsCard from './components/ApprovalsCard'
import ActivityLogCard from '../detail/components/ActivityLogCard'
import AdditionalInfoCard from '../detail/components/AdditionalInfoCard'
import SegmentInfoCard from '../detail/components/SegmentInfoCard'
import UnitCard from '../detail/components/UnitCard'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import RequestDetailCard from '../detail/components/RequestDetailCard'
import { pdf } from '@react-pdf/renderer'
import SrPdfDocument from '../detail/components/SrPdfDocument'

const ServiceRequisitionApprovalDetailPage = () => {
  const { userProfile } = useAuth()
  const { srData: rawSrData, fetchSrList, logList, fetchWorkOrder, workOrder: rawWorkOrder } = useSr()

  const [srData, setSrData] = useState<ServiceRequisition>(rawSrData)

  const { mutate: readMutate } = useReadSrApproval()

  const ownApproval = srData?.approvals?.find(approval => approval.userId === userProfile?.id)

  const handlePrint = async () => {
    if (srData) {
      console.log(srData)
      const blob = await pdf(
        <SrPdfDocument segment={srData?.workOrderSegment} woData={srData?.workOrder} srData={srData} />
      ).toBlob()
      const url = URL.createObjectURL(blob)
      const printWindow = window.open(url, '_blank')
      printWindow.onload = () => {
        printWindow.print()
        printWindow.onafterprint = () => {
          printWindow.close()
        }
      }
    }
  }

  useEffect(() => {
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          isRead: true,
          srId: srData?.id,
          approvalId: ownApproval.id
        },
        { onSuccess: () => fetchSrList() }
      )
    }
  }, [srData])

  useEffect(() => {
    setSrData(rawSrData)
  }, [rawSrData])

  useEffect(() => {
    if (srData?.workOrderId) {
      fetchWorkOrder()
    }
  }, [srData?.workOrderId])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Service Request</Typography>
          </Link>
          <Link to='/service-request/approval' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Persetujuan Service Request</Typography>
          </Link>
          <Typography>Detil Service Request</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>No. SR: {srData?.number}</Typography>
              {ownApproval && (
                <>
                  {srData?.status !== ServiceRequisitionStatus.CANCELED ? (
                    <Chip
                      label={srStatusOptions.find(status => status.value === srData?.status)?.label}
                      color={statusChipColor[srData?.status]?.color}
                      variant='tonal'
                      size='small'
                    />
                  ) : (
                    <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
                  )}
                </>
              )}
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(srData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 is-full sm:is-auto'>
            {/* <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
             */}
            <Button
              onClick={handlePrint}
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button>
          </div>
        </div>
      </Grid>
      {srData?.items ? (
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
      ) : null}
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DocNumberCard
              docType='WO'
              warehouseDoc={rawWorkOrder}
              note={`Segment No. ${srData?.workOrderSegment?.number}`}
            />
          </Grid>
          <Grid item xs={12}>
            <AdditionalInfoCard />
          </Grid>
          <Grid item xs={12}>
            <SegmentInfoCard />
          </Grid>
          <Grid item xs={12}>
            <UnitCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {srData?.approvals ? (
            <Grid item xs={12}>
              <ApprovalsCard />
            </Grid>
          ) : null}
          <Grid item xs={12}>
            <RequestDetailCard />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={logList} />
          </Grid>
          <Grid item xs={12}>
            <OwnerCard user={srData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default ServiceRequisitionApprovalDetailPage
