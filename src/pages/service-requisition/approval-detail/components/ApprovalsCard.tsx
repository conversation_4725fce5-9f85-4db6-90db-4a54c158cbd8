// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { <PERSON><PERSON>, <PERSON> } from '@mui/material'

// Type Imports
import { ThemeColor } from '@/core/types'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'
import { ApproverType } from '@/types/userTypes'
import EditApproverDialog, { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useSr } from '../../context/SrContext'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { ServiceRequisitionApprovalStatus, ServiceRequisitionStatus } from '@/types/serviceRequisitionsTypes'
import { useUpdateSrApprovalStatus, useUpdateSrApprover } from '@/api/services/service-requisitions/mutation'

type StatusChipType = {
  label: string
  color: ThemeColor
}

// Vars
export const statusChipValue: { [key: string]: StatusChipType } = {
  WAITING: { label: 'Menunggu', color: 'secondary' },
  PENDING: { label: 'Menunggu', color: 'secondary' },
  APPROVED: { label: 'Disetujui', color: 'success' },
  REJECTED: { label: 'Ditolak', color: 'error' }
}

const ApprovalsCard = () => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { srData, fetchSrData, fetchSrList, fetchLogList, canUpdate } = useSr()
  const { mutate: updateStatusMutate } = useUpdateSrApprovalStatus()
  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdateSrApprover()

  const [{ open: editApproverOpen, selectedApproval }, setEditApproverModalState] = useState<{
    open: boolean
    selectedApproval?: ApproverType
  }>({
    open: false
  })

  const approvalList = srData?.approvals ?? []

  let ownApprovalIndex = -1
  const ownApproval = srData?.approvals?.find((approval, index) => {
    ownApprovalIndex = index
    return approval.userId === userProfile?.id
  })

  const handleApprove = (id: number) => {
    setConfirmState({
      open: true,
      title: 'Setujui Service Request',
      content: 'Apakah kamu yakin akan menyetujui Service Request ini? Action ini tidak bisa diubah ',
      confirmText: 'Setujui Service Request',
      onConfirm: () => {
        updateStatusMutate(
          {
            srId: srData?.id,
            approvalId: id,
            status: ServiceRequisitionApprovalStatus.APPROVED
          },
          {
            onSuccess: () => {
              toast.success('Service Request berhasil disetujui')
              fetchSrData()
              fetchLogList()
              fetchSrList()
            }
          }
        )
      }
    })
  }

  const handleReject = (id: number) => {
    setConfirmState({
      open: true,
      title: 'Tolak Service Request',
      content: 'Apakah kamu yakin akan menolak Service Request ini? Action ini tidak bisa diubah ',
      confirmText: 'Tolak Service Request',
      onConfirm: () => {
        updateStatusMutate(
          {
            srId: srData?.id,
            approvalId: id,
            status: ServiceRequisitionApprovalStatus.REJECTED
          },
          {
            onSuccess: () => {
              toast.success('Service Request berhasil ditolak')
              fetchSrData()
              fetchLogList()
              fetchSrList()
            }
          }
        )
      },
      confirmColor: 'error'
    })
  }

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        srId: srData?.id,
        approvalId: selectedApproval?.id,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchSrData()
          fetchLogList()
          setEditApproverModalState({ open: false })
        }
      }
    )
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Pengajuan Persetujuan</Typography>
          </div>
          <div className='flex flex-col gap-4'>
            {approvalList.map((approval, index) => {
              const statusValue = statusChipValue[approval.status]
              return (
                <div
                  key={approval.id}
                  className='rounded-lg border border-[#4c4e64]/22 p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'
                >
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {approval.user?.fullName}
                    </p>
                    {ownApproval?.status === ServiceRequisitionApprovalStatus.WAITING &&
                    approval.status === ServiceRequisitionApprovalStatus.WAITING &&
                    srData?.status !== ServiceRequisitionStatus.CANCELED ? (
                      <div className='hidden md:flex gap-2 items-center self-center'>
                        <Button
                          variant='contained'
                          size='small'
                          color='error'
                          onClick={() => handleReject(approval.id)}
                        >
                          Tolak
                        </Button>
                        <Button variant='contained' size='small' onClick={() => handleApprove(approval.id)}>
                          Setujui
                        </Button>
                      </div>
                    ) : (
                      <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
                    )}
                  </div>
                  <div className='flex justify-between items-start w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>{approval.user?.title}</small>
                    </label>
                    {approval.respondedAt ? (
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>
                          {formatDate(approval.respondedAt, 'dd MMM yyyy, HH:mm', { locale: id })}
                        </small>
                      </label>
                    ) : null}
                  </div>
                  {ownApproval?.status === ServiceRequisitionApprovalStatus.WAITING &&
                    approval.status === ServiceRequisitionApprovalStatus.WAITING && (
                      <div className='flex md:hidden justify-between gap-2 w-full mt-2'>
                        <Button
                          variant='contained'
                          fullWidth
                          size='small'
                          color='error'
                          onClick={() => handleReject(approval.id)}
                        >
                          Tolak
                        </Button>
                        <Button variant='contained' fullWidth size='small' onClick={() => handleApprove(approval.id)}>
                          Setujui
                        </Button>
                      </div>
                    )}
                  {!!approval?.rejectionNote && (
                    <div>
                      <small>Catatan:</small>
                      <Typography>{approval?.rejectionNote}</Typography>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {editApproverOpen ? (
        <EditApproverDialog
          open={editApproverOpen}
          setOpen={open =>
            setEditApproverModalState(current => ({
              open: open,
              selectedApproval: open ? current.selectedApproval : undefined
            }))
          }
          selectedApproval={selectedApproval}
          scope={DefaultApprovalScope.ServiceRequisition}
          onSubmit={handleUpdateApprover}
          isLoading={updateApproverLoading}
          approvalList={approvalList}
        />
      ) : null}
    </>
  )
}

export default ApprovalsCard
