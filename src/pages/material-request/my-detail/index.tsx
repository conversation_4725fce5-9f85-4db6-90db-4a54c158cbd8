// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadc<PERSON>bs, Button, Card, CardContent, Chip, Typography } from '@mui/material'

import { useMr } from '../context/MrContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { mrStatusOptions } from '../list/config/utils'
import { statusChipColor } from '../list/config/table'
import { MrStatus } from '@/types/mrTypes'
import { Link } from 'react-router-dom'
import ItemListCard from '../approval-detail/components/ItemListCard'
import { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { toast } from 'react-toastify'
import { useUpdateMrApprover } from '@/api/services/mr/mutation'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import UnitCard from '../create/components/UnitCard'
import GeneralPurchaseCard from '../detail/components/GeneralPurchaseCard'
import CreateByWoCard from '../detail/components/CreateByWoCard'
import DocumentList from '../detail/components/DocumentList'
import AdditionalInfoCard from '../detail/components/AdditionalInfoCard'
import OwnerCard from '../detail/components/OwnerCard'
import ApprovalDetailCard from '../detail/components/ApprovalDetailCard'
import ActivityLogCard from '../detail/components/ActivityLogCard'

const MrDetailPage = () => {
  const router = useRouter()
  const { accountPermissions, userProfile } = useAuth()
  const { mrData, handleCancelMr, logList, canRemove, fetchMrData, fetchLogList } = useMr()

  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdateMrApprover()

  const canMgOut =
    accountPermissions.includes('outgoing-material.create') &&
    mrData?.createdBy === userProfile?.id &&
    mrData?.status === MrStatus.APPROVED

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        mrId: mrData?.id,
        approvalId: formData.approvalId,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchMrData()
          fetchLogList()
        },
        onError: error => {
          const message = error?.message
          if (message) {
            toast.error(message)
          } else {
            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
          }
        }
      }
    )
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Material Request</Typography>
          </Link>
          <Link to='/mr/my-list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Request Saya</Typography>
          </Link>
          <Typography>Detil Material Request</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>NO. MR: {mrData?.number}</Typography>
              <Chip
                label={mrStatusOptions.find(option => option.value === mrData?.status)?.label}
                color={statusChipColor[mrData?.status]?.color}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(mrData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button> */}
            </div>
            {mrData?.status === MrStatus.PROCESSED && canRemove && (
              <Button color='error' variant='contained' className='is-full sm:is-auto' onClick={handleCancelMr}>
                Batalkan Material Request
              </Button>
            )}
            {canMgOut && (
              <Button
                variant='contained'
                className='is-full sm:is-auto'
                disabled={(mrData?.itemsStock ?? 0) === 0}
                onClick={() => {
                  router.push(`/mr/my-list/${mrData?.id}/create-mg-out`)
                }}
              >
                Buat Permintaan Keluar
              </Button>
            )}
            {mrData?.status === MrStatus.APPROVED ||
              (mrData?.status === MrStatus.CLOSED && (
                <Button
                  variant='contained'
                  className='is-full sm:is-auto'
                  onClick={() => {
                    router.push(`/sr/request/${mrData?.id}`)
                  }}
                >
                  Kembalikan Stok
                </Button>
              ))}
          </div>
        </div>
      </Grid>
      {mrData?.items && (
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
      )}
      <Grid item xs={12} md={8}>
        <Grid container spacing={4}>
          {mrData?.generalPurchases?.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Grid container spacing={3}>
                    {mrData.generalPurchases.map(purchase => (
                      <Grid item xs={12}>
                        <GeneralPurchaseCard
                          key={JSON.stringify(purchase)}
                          purchase={purchase}
                          fetchDetail={() => fetchMrData()}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}

          {mrData?.workOrderSegment && (
            <Grid item xs={12}>
              <CreateByWoCard mrData={mrData} />
            </Grid>
          )}
          {mrData?.createdDocumentsCount > 0 ? (
            <Grid item xs={12}>
              <DocumentList />
            </Grid>
          ) : null}
          {mrData?.unit && (
            <Grid item xs={12}>
              <UnitCard warehouseData={mrData} />
            </Grid>
          )}
          <Grid item xs={12}>
            <AdditionalInfoCard warehouseData={mrData} />
          </Grid>
          <Grid item xs={12}>
            <OwnerCard user={mrData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={4}>
        <Grid container spacing={4}>
          {mrData?.approvals ? (
            <Grid item xs={12}>
              <ApprovalDetailCard
                approvalList={mrData?.approvals ?? []}
                handleUpdateApprover={handleUpdateApprover}
                updateApproverLoading={updateApproverLoading}
              />
            </Grid>
          ) : null}
          <Grid item xs={12}>
            <ActivityLogCard logList={logList} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default MrDetailPage
