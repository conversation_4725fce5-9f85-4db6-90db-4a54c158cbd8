import { PurchaseInvoiceDiscountType } from '@/types/purchaseInvoiceTypes'
import { isNullOrUndefined } from '@/utils/helper'
import { z } from 'zod'

export const createPurchaseInvoiceSchemaDto = z
  .object({
    vendorId: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
    paymentTerms: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
    paymentDueDays: z.number().optional().nullable(),
    paymentDueDate: z.string().optional().nullable(),
    vendorNumber: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
    vendorName: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
    invoiceDate: z.string().min(1, 'Tanggal Faktur wajib diisi'),
    note: z.string().optional().nullable(),
    isDownPayment: z.boolean(),
    currencyId: z.string().nullable().optional(),
    exchangeRate: z.number().nullable().optional(),
    documentUploadId: z.string().optional().nullable(),
    documentContent: z.string().optional().nullable(),
    documentName: z.string().optional().nullable(),
    isGeneralPurchase: z.boolean().optional().nullable(),
    discountType: z.nativeEnum(PurchaseInvoiceDiscountType).optional().nullable(),
    discountValue: z.number().optional().nullable(),
    taxInvoiceNumber: z.string().optional().nullable(),
    taxInvoiceDate: z.string().optional().nullable(),
    orders: z
      .array(
        z.object({
          purchaseOrderId: z.string(),
          incomingMaterialId: z.string().nullable().optional(),
          items: z
            .array(
              z.object({
                incomingMaterialItemId: z.number().nullable().optional(),
                pricePerUnit: z.number(),
                quantity: z.number(),
                taxId: z.string().optional().nullable(),
                taxType: z.string().optional().nullable(),
                taxPercentage: z.number().optional().nullable()
              })
            )
            .nullable()
            .optional(),
          totalAmount: z.number().nullable().optional(),
          downPaymentAmount: z.number().nullable().optional(),
          downPaymentIds: z.array(z.number()).nullable().optional()
        })
      )
      .min(1, { message: 'Wajib Dipilih' }),
    otherExpenses: z
      .array(
        z.object({
          accountId: z.string().min(1, 'Akun perkiraan wajib dipilih'),
          siteId: z.string().optional().nullable(),
          departmentId: z.string().optional().nullable(),
          amount: z.number(),
          note: z.string().optional()
        })
      )
      .nullable()
      .optional(),
    departmentId: z.string().nullable().optional(),
    projectId: z.string().nullable().optional(),
    projectLabelId: z.number().nullable().optional(),
    otherExpensesTotal: z.number().optional().nullable(),
    discountAmount: z.number().optional().nullable(),
    subtotal: z.number().optional().nullable(),
    grandTotal: z.number().optional().nullable()
  })
  .superRefine((data, ctx) => {
    if (!!data.isGeneralPurchase && isNullOrUndefined(data.vendorName)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Wajib diisi',
        path: ['vendorName']
      })
    }
    if (!data.isGeneralPurchase && isNullOrUndefined(data.vendorId)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Wajib diisi',
        path: ['vendorId']
      })
    }
  })

export type PurchaseInvoiceDtoType = z.infer<typeof createPurchaseInvoiceSchemaDto>
