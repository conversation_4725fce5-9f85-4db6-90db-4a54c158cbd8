import {
  <PERSON>rid,
  <PERSON><PERSON>ield,
  Card,
  CardContent,
  Typography,
  Autocomplete,
  CircularProgress,
  Checkbox,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  ListSubheader,
  ListItemText,
  InputAdornment,
  IconButton
} from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useState, useCallback, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'

import { PurchaseInvoiceDtoType } from '../config/schema'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { VendorType } from '@/types/companyTypes'
import CompanyQueryMethods, {
  PROJECT_LABEL_LIST_QUERY_KEY,
  PROJECT_LIST_QUERY_KEY,
  VENDOR_LIST_QUERY_KEY,
  VENDOR_QUERY_KEY
} from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { PurchaseOrderPaymentMethod } from '@/pages/purchase-order/config/enum'
import { add, addDays, differenceInDays, formatISO, startOfDay, toDate } from 'date-fns'
import { ProjectLabelType, ProjectStatus, ProjectType } from '@/types/projectTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useSearchParams } from 'react-router-dom'
import NumberField from '@/components/numeric/NumberField'
import CurrencyField from '@/components/numeric/CurrencyField'

import debounce from '@mui/material/utils/debounce'
const InvoiceDetail = () => {
  const { control, setValue } = useFormContext<PurchaseInvoiceDtoType>()
  const { groupedSiteList, departmentList, currenciesList } = useAuth()
  const [searchParams] = useSearchParams()
  const [vendorSearchQuery, setVendorSearchQuery] = useState('')
  const [selectedVendor, setSelectedVendor] = useState<VendorType | null>(null)

  // Watch form values
  const paymentMethodWatch = useWatch({
    control,
    name: 'paymentTerms',
    defaultValue: ''
  })

  const isGeneralPurchaseWatch = useWatch({
    control,
    name: 'isGeneralPurchase',
    defaultValue: false
  })

  const invoiceDateWatch = useWatch({
    control,
    name: 'invoiceDate'
  })

  const paymentDueDaysWatch = useWatch({
    control,
    name: 'paymentDueDays'
  })

  const currencyIdWatch = useWatch({
    control,
    name: 'currencyId'
  })

  // Watch vendorId to pre-fill from draft
  const vendorIdWatch = useWatch({
    control,
    name: 'vendorId'
  })

  // Fetch vendor list
  const { data: vendorListResponse = defaultListData as ListResponse<VendorType>, isFetching: fetchVendorsLoading } =
    useQuery({
      queryKey: [VENDOR_LIST_QUERY_KEY, vendorSearchQuery],
      queryFn: () => {
        return CompanyQueryMethods.getVendorList({
          ...(vendorSearchQuery && { search: vendorSearchQuery }),
          limit: 30
        })
      },
      placeholderData: defaultListData as ListResponse<VendorType>
    })

  const vendorList = vendorListResponse.items || []

  // Debounced search handler
  const handleVendorInputChange = useCallback(
    debounce((_event: any, newValue: string, reason: string) => {
      if (reason === 'input') {
        setVendorSearchQuery(newValue)
      }
    }, 700),
    []
  )

  // Pull vendor detail when vendorId is present (e.g., draft rehydration)
  const { data: vendorDetail } = useQuery({
    enabled: !!vendorIdWatch && !isGeneralPurchaseWatch,
    queryKey: [VENDOR_QUERY_KEY, vendorIdWatch],
    queryFn: () => CompanyQueryMethods.getVendor(vendorIdWatch as string)
  })

  useEffect(() => {
    if (vendorDetail) {
      setSelectedVendor(vendorDetail)
      setValue('vendorName', vendorDetail.name)
    }
  }, [vendorDetail])

  const currentCurrency = currenciesList?.find(c => c.id === currencyIdWatch)

  useEffect(() => {
    if (currencyIdWatch && currenciesList?.find(c => c.id === currencyIdWatch)?.isDefault) {
      setValue('exchangeRate', 1)
    }
  }, [currencyIdWatch, currenciesList])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Detil Faktur</Typography>

        <Grid container spacing={4}>
          {/* Vendor Selection */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='isGeneralPurchase'
              render={({ field: { value, onChange } }) => (
                <FormControlLabel
                  label='Pembelian di Vendor Umum'
                  control={<Checkbox checked={value} onChange={onChange} />}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {!!isGeneralPurchaseWatch ? (
              <Controller
                control={control}
                name='vendorName'
                render={({ field, fieldState: { error } }) => (
                  <TextField {...field} label='Nama Vendor' error={!!error} fullWidth />
                )}
              />
            ) : (
              <Controller
                control={control}
                name='vendorId'
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <Autocomplete
                    key={JSON.stringify(selectedVendor)}
                    filterOptions={x => x}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    onInputChange={handleVendorInputChange}
                    options={vendorList}
                    freeSolo
                    onChange={(_e, newValue: VendorType | null) => {
                      if (newValue) {
                        setSelectedVendor(newValue)
                        onChange(newValue.id)
                        setValue('vendorName', newValue.name)
                      } else {
                        setSelectedVendor(null)
                        onChange('')
                        setValue('vendorName', '')
                      }
                    }}
                    value={selectedVendor || vendorList.find(v => v.id === value) || null}
                    noOptionsText='Vendor tidak ditemukan'
                    loading={fetchVendorsLoading}
                    renderInput={params => (
                      <TextField
                        {...params}
                        label='Vendor'
                        placeholder='Cari Vendor'
                        variant='outlined'
                        error={!!error}
                        helperText={error?.message}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {fetchVendorsLoading ? <CircularProgress size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                      />
                    )}
                    getOptionLabel={(option: VendorType) => option?.name || ''}
                    renderOption={(props, option) => {
                      const { key, ...optionProps } = props
                      return (
                        <li key={key} {...optionProps}>
                          <Typography>
                            {option.code} - {option.name}
                          </Typography>
                        </li>
                      )
                    }}
                  />
                )}
              />
            )}
          </Grid>

          {/* Invoice Number */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='vendorNumber'
              rules={{ required: 'No. Faktur Vendor wajib diisi' }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='No. Faktur Vendor'
                  placeholder='Isi nomor faktur dari vendor'
                  error={!!error}
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          {/* Invoice Date */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='invoiceDate'
              rules={{ required: 'Tanggal Faktur wajib diisi' }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Faktur'
                      placeholder='19/05/2025'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>

          {/* Payment Method */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='paymentTerms'
              rules={{ required: 'Metode Bayar wajib dipilih' }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel>Metode Bayar</InputLabel>
                  <Select key={value} value={value} onChange={onChange} label='Metode Bayar'>
                    {paymentMethodOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {error && <FormHelperText>{error.message}</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>

          {/* Payment Due Date - only show if payment method is NET */}
          {paymentMethodWatch === PurchaseOrderPaymentMethod.NET && (
            <>
              <Grid item xs={12} md={6}>
                <Controller
                  name='paymentDueDays'
                  control={control}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <TextField
                      {...{ value, onChange }}
                      label='Hari'
                      InputProps={{ inputComponent: NumberField as any, endAdornment: 'Hari' }}
                      className='w-full'
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='paymentDueDate'
                  rules={{ required: 'Tanggal Jatuh Tempo wajib diisi' }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <AppReactDatepicker
                      key={paymentDueDaysWatch}
                      boxProps={{ className: 'is-full' }}
                      selected={
                        value
                          ? toDate(value)
                          : paymentDueDaysWatch
                            ? addDays(invoiceDateWatch || new Date(), paymentDueDaysWatch)
                            : undefined
                      }
                      onChange={(date: Date) => {
                        onChange(date.toISOString())
                      }}
                      disabled={!invoiceDateWatch}
                      dateFormat='dd/MM/yyyy'
                      minDate={startOfDay(addDays(invoiceDateWatch, 1))}
                      customInput={
                        <TextField
                          fullWidth
                          label='Tanggal Jatuh Tempo'
                          error={!!error}
                          helperText={error?.message}
                          InputProps={{
                            readOnly: true
                          }}
                        />
                      }
                    />
                  )}
                />
              </Grid>
            </>
          )}

          {/* Tax Invoice Number */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='taxInvoiceNumber'
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='No Faktur Pajak'
                  variant='outlined'
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='clear'
                          onClick={() => {
                            field.onChange('')
                          }}
                        >
                          <i className='fa fa-times-circle' />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              )}
            />
          </Grid>
          {/* Tax Invoice Date */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='taxInvoiceDate'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      aria-readonly
                      label='Tanggal Faktur Pajak'
                      placeholder='Pilih tanggal'
                      error={!!error}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>

          {currencyIdWatch && (
            <>
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='currencyId'
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth>
                      <InputLabel id='mata-uang-field'>Mata Uang</InputLabel>
                      <Select
                        {...field}
                        fullWidth
                        disabled
                        label='Mata Uang'
                        placeholder='Mata Uang'
                        className='bg-white'
                      >
                        {currenciesList?.map(curr => (
                          <MenuItem key={curr.id} value={curr.id}>
                            {curr.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='exchangeRate'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      aria-readonly
                      disabled={!!currentCurrency?.isDefault}
                      label='Konversi ke Rupiah'
                      InputLabelProps={{
                        shrink: !!field.value
                      }}
                      InputProps={{
                        inputComponent: CurrencyField as any,
                        inputProps: {
                          prefix: 'Rp',
                          name: 'exchangeRate',
                          value: field.value,
                          allowLeadingZeros: false
                        }
                      }}
                    />
                  )}
                />
              </Grid>
            </>
          )}

          {/* Down Payment Checkbox */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='isDownPayment'
              render={({ field: { value, onChange } }) => (
                <FormControlLabel
                  control={<Checkbox checked={value} onChange={e => onChange(e.target.checked)} />}
                  label='Pembayaran Untuk Uang Muka'
                />
              )}
            />
          </Grid>

          {/* Memo */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Memo'
                  placeholder='Tambahkan catatan (opsional)'
                  multiline
                  rows={2}
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetail
