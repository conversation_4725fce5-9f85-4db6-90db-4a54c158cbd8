import { createContext, ReactN<PERSON>, useContext, useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { PurchaseInvoice, PurchaseInvoiceParams, PurchaseInvoiceLog } from '@/types/purchaseInvoiceTypes'
import usePartialState from '@/core/hooks/usePartialState'
import PurchaseInvoiceQueryMethods, {
  PURCHASE_INVOICE_LIST_QUERY_KEY,
  PURCHASE_INVOICE_DETAIL_QUERY_KEY,
  PURCHASE_INVOICE_LOGS_QUERY_KEY
} from '@/api/services/purchase-invoice/query'
import { usePathname } from '@/routes/hooks'
import { useAuth } from '@/contexts/AuthContext'

interface SelectedPurchaseInvoice {
  purchaseInvoiceId?: string
}

interface PurchaseInvoiceContextProps {
  purchaseInvoiceListResponse: ListResponse<PurchaseInvoice>
  purchaseInvoiceParams: PurchaseInvoiceParams
  setPurchaseInvoiceParams: (params: PurchaseInvoiceParams) => void
  setPartialPurchaseInvoiceParams: (key: keyof PurchaseInvoiceParams, value: any) => void
  setSelectedPurchaseInvoice: (selected: SelectedPurchaseInvoice) => void
  selectedPurchaseInvoiceId?: string
  fetchPurchaseInvoiceList: () => void
  // Detail page data
  purchaseInvoiceData?: PurchaseInvoice
  fetchPurchaseInvoiceData: () => void
  canView: boolean
  canUpdate: boolean
  purchaseInvoiceLogs: PurchaseInvoiceLog[]
  fetchPurchaseInvoiceLogs: () => void
}

export const PurchaseInvoiceContext = createContext<PurchaseInvoiceContextProps>({} as PurchaseInvoiceContextProps)

interface PurchaseInvoiceContextProviderProps {
  children: ReactNode
}

export const usePurchaseInvoice = () => {
  return useContext(PurchaseInvoiceContext)
}

export function PurchaseInvoiceContextProvider({ children }: PurchaseInvoiceContextProviderProps) {
  const { purchaseInvoiceId } = useParams()
  const { ownSiteList } = useAuth()

  const pathname = usePathname()

  const isApprovalPage = pathname.includes('/purchase-invoice/approval')

  const [purchaseInvoiceParams, setPurchaseInvoiceParams] = useState<PurchaseInvoiceParams>({
    limit: 10,
    page: 1,
    search: ''
  })

  const [selectedPurchaseInvoice, setSelectedPurchaseInvoice] = useState<SelectedPurchaseInvoice>({
    purchaseInvoiceId: purchaseInvoiceId
  })

  const [, setPartialPurchaseInvoiceParams] = usePartialState(setPurchaseInvoiceParams)

  // Fetch purchase invoice list for approval
  const { data: purchaseInvoiceListResponse, refetch: fetchPurchaseInvoiceList } = useQuery({
    enabled: !!purchaseInvoiceParams?.siteIds,
    queryKey: [PURCHASE_INVOICE_LIST_QUERY_KEY, JSON.stringify(purchaseInvoiceParams), isApprovalPage],
    queryFn: () => {
      if (isApprovalPage) {
        return PurchaseInvoiceQueryMethods.getToMePurchaseInvoices(purchaseInvoiceParams)
      } else {
        return PurchaseInvoiceQueryMethods.getPaginatedPurchaseInvoices(purchaseInvoiceParams)
      }
    },
    placeholderData: defaultListData as ListResponse<PurchaseInvoice>
  })

  // Fetch purchase invoice detail
  const { data: purchaseInvoiceData, refetch: fetchPurchaseInvoiceData } = useQuery({
    queryKey: [PURCHASE_INVOICE_DETAIL_QUERY_KEY, purchaseInvoiceId],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoice(purchaseInvoiceId!),
    enabled: !!purchaseInvoiceId
  })

  const { data: purchaseInvoiceLogs, refetch: fetchPurchaseInvoiceLogs } = useQuery({
    queryKey: [PURCHASE_INVOICE_LOGS_QUERY_KEY, purchaseInvoiceId],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoiceLogs(purchaseInvoiceId!),
    enabled: !!purchaseInvoiceId
  })

  useEffect(() => {
    setSelectedPurchaseInvoice(curr => ({ ...curr, purchaseInvoiceId: purchaseInvoiceId }))
  }, [purchaseInvoiceId])

  useEffect(() => {
    setPurchaseInvoiceParams({
      ...purchaseInvoiceParams,
      siteIds: ownSiteList.map(site => site.id).join(',')
    })
  }, [ownSiteList])

  // Permission checks (simplified for now)
  const canView = true
  const canUpdate = true

  const value = {
    purchaseInvoiceListResponse: purchaseInvoiceListResponse ?? defaultListData,
    purchaseInvoiceParams,
    setPurchaseInvoiceParams,
    setPartialPurchaseInvoiceParams,
    setSelectedPurchaseInvoice,
    selectedPurchaseInvoiceId: selectedPurchaseInvoice.purchaseInvoiceId,
    fetchPurchaseInvoiceList,
    // Detail page data
    purchaseInvoiceData,
    fetchPurchaseInvoiceData,
    canView,
    canUpdate,
    purchaseInvoiceLogs: purchaseInvoiceLogs ?? [],
    fetchPurchaseInvoiceLogs
  }

  return (
    <PurchaseInvoiceContext.Provider value={value}>
      <>{children}</>
    </PurchaseInvoiceContext.Provider>
  )
}
