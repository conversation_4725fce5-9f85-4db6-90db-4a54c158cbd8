import { Document, Page, View, Text, StyleSheet, Image } from '@react-pdf/renderer'
import { Table, TableHeader, TableCell, TableRow } from '@ag-media/react-pdf-table'
import { ReactNode } from 'react'
import { PoType } from '@/pages/purchase-order/config/types'
import { formatDate } from 'date-fns'
import { formatThousandSeparator, thousandSeparator, toCurrency, toTerbilang } from '@/utils/helper'
import { VendorType } from '@/types/companyTypes'
import { id } from 'date-fns/locale'
import { colors } from '@mui/material'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { PurchaseInvoice } from '@/types/purchaseInvoiceTypes'
import { calculateTax } from '@/utils/calculate-tax'

// Styles
export const pdfStyles = StyleSheet.create({
  page: {
    padding: 30,
    color: '#323639'
  },
  textNormal: {
    fontSize: 9
  },
  textHeading: {
    fontSize: 20,
    fontWeight: 700,
    lineHeight: 1.2
  },
  textBold: {
    fontFamily: 'Helvetica-Bold',
    fontWeight: 700,
    fontSize: 9
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 5,
    marginBottom: 20
  },
  section: {
    display: 'flex',
    flexDirection: 'column',
    gap: 3,
    alignItems: 'flex-start',
    justifyContent: 'space-between'
  },
  flexRowBetween: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row'
  },
  flexCol: {
    display: 'flex',
    flexDirection: 'column'
  },
  border: {
    border: '1px solid #000',
    borderRadius: 4,
    padding: 4
  },
  textAddress: {
    fontSize: 8,
    width: '100%'
  },
  poInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15
  },
  table: {
    width: '100%',
    marginBottom: 15,
    border: '1px solid #000'
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTop: '1px solid black'
  },
  tableHeader: {
    fontWeight: 'bold',
    fontSize: 8,
    borderTop: '1px solid black'
  },
  tableCell: {
    fontSize: 8,
    padding: 6,
    border: 'none'
  },
  textRight: {
    textAlign: 'right',
    width: '100%'
  },
  textCenter: {
    textAlign: 'center',
    width: '100%'
  },
  totals: {
    fontSize: 10,
    marginLeft: 'auto',
    width: '30%',
    display: 'flex',
    flexDirection: 'column',
    gap: 4
  },
  signature: {
    marginTop: 20,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  signatureItem: {
    fontSize: 10,
    minHeight: 65,
    maxWidth: 120,
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  signatureName: {
    paddingTop: 4,
    fontSize: 9
  }
})

const headerProduct = ['Kode Barang', 'Nama Item', 'Harga Satuan', 'Qty', 'Pajak', 'Total']
const weightTable = [0.8, 1.1, 0.8, 0.4, 0.7, 0.7, 1.0]

export const TextItemFlex = ({ children, end, style }: { children: ReactNode; end?: boolean; style?: any }) => (
  <Text
    style={{
      width: '50%',
      fontSize: 9,
      ...(end ? { textAlign: 'right' } : {}),
      ...style
    }}
  >
    {children}
  </Text>
)

const InvoicePdfDocument = ({
  purchaseInvoice,
  poData,
  vendor,
  qrCode,
  isPreview = false
}: {
  purchaseInvoice: PurchaseInvoice
  poData: PoType[]
  vendor: VendorType
  isPreview?: boolean
  qrCode?: string
}) => {
  return (
    <Document>
      <Page size='A4' style={pdfStyles.page}>
        {/* Header Perusahaan */}
        <View style={[pdfStyles.flexRow, { justifyContent: 'space-between', gap: 4 }]}>
          <View style={[pdfStyles.border, pdfStyles.flexRow, { gap: 8 }]}>
            <Image src='/rpu-logo.png' style={{ width: 32, height: 32 }} />
            <View style={[pdfStyles.flexCol]}>
              <Text style={[pdfStyles.textBold, { color: '#000' }]}>PT. RIMBA PERKASA UTAMA</Text>
              <View style={{ marginVertical: 2 }} />
              <Text style={pdfStyles.textAddress}>Jl. Danau Toba No. 148</Text>
              <Text style={[pdfStyles.textAddress, { marginTop: 2 }]}>Samarinda</Text>
            </View>
          </View>
          <Text style={[pdfStyles.textBold, { fontSize: 14, color: '#000', paddingHorizontal: 8 }]}>
            Faktur Pembelian
          </Text>
        </View>

        <View style={{ marginVertical: 14 }} />

        <View style={pdfStyles.flexRow}>
          <View style={[pdfStyles.flexCol, { gap: 4, width: '50%' }]}>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Nomor Faktur</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>{purchaseInvoice?.number}</TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Faktur Vendor</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>{purchaseInvoice?.vendorNumber}</TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Tanggal</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>
                {formatDate(purchaseInvoice?.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}
              </TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Lokasi</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>{purchaseInvoice?.site?.name ?? '-'}</TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Departemen</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>{purchaseInvoice?.department?.name ?? '-'}</TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Label Proyek</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>{purchaseInvoice?.projectLabel?.name ?? '-'}</TextItemFlex>
            </View>
          </View>
          <View style={[pdfStyles.flexCol, { gap: 4, width: '50%' }]}>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Metode Bayar</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>
                {paymentMethodOptions.find(option => option.value === purchaseInvoice?.paymentTerms)?.label ?? '-'}{' '}
                {purchaseInvoice?.paymentDueDays || ''}
              </TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Tgl Jatuh Tempo</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>
                {purchaseInvoice?.paymentDueDate
                  ? formatDate(purchaseInvoice?.paymentDueDate, 'dd MMM yyyy', { locale: id })
                  : '-'}
              </TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Mata Uang</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>{purchaseInvoice?.currency?.name}</TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Nilai Tukar</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>{toCurrency(purchaseInvoice?.exchangeRate ?? 0)}</TextItemFlex>
            </View>
          </View>
        </View>

        <View style={{ marginVertical: 10, borderTop: '1px solid #323639' }} />

        <View style={[pdfStyles.flexRow, { fontSize: 10, gap: 8 }]}>
          <View style={[pdfStyles.flexCol, { flexGrow: 1 }]}>
            <Text style={pdfStyles.textNormal}>Vendor:</Text>
            <Text style={[pdfStyles.textBold, { marginTop: 4 }]}>{purchaseInvoice?.vendor?.name}</Text>
            <View style={{ marginVertical: 2 }} />
            <Text style={pdfStyles.textNormal}>{vendor?.taxplayerNumber}</Text>
            <Text style={{ marginTop: 2 }}>{vendor?.addresses?.find(add => add.isDefault)?.address ?? '-'}</Text>
          </View>
          {/* <View style={[pdfStyles.flexCol, { flexGrow: 1 }]}>
            <Text style={pdfStyles.textNormal}>Kepada:</Text>
            <Text style={[pdfStyles.textBold, { marginTop: 4 }]}>{purchaseInvoice?.vendor?.name}</Text>
            <View style={{ marginVertical: 2 }} />
            <Text style={pdfStyles.textNormal}>{vendor?.taxplayerNumber}</Text>
            <Text style={{ marginTop: 2 }}>{vendor?.addresses?.find(add => add.isDefault)?.address ?? '-'}</Text>
          </View> */}
        </View>

        <View style={{ marginVertical: 10 }} />

        {/* Tabel Items */}
        <Table tdStyle={{ padding: '10px', textAlign: 'center' }} style={pdfStyles.table} weightings={weightTable}>
          <TableHeader fixed style={pdfStyles.tableHeader}>
            {purchaseInvoice?.isDownPayment
              ? ['Nama Item', 'Total'].map(header => (
                  <TableCell key={`$indexHeader`} style={[pdfStyles.tableCell, pdfStyles.textBold]}>
                    <Text style={'Total'.includes(header) ? pdfStyles.textRight : {}}>{header}</Text>
                  </TableCell>
                ))
              : headerProduct.map(header => (
                  <TableCell key={`$indexHeader}`} style={[pdfStyles.tableCell, pdfStyles.textBold]}>
                    <Text
                      style={
                        ['Qty', 'Harga Satuan', 'Diskon', 'Total', 'Pajak'].includes(header) ? pdfStyles.textRight : {}
                      }
                    >
                      {header}
                    </Text>
                  </TableCell>
                ))}
          </TableHeader>

          {purchaseInvoice?.orders.map((orderItem, pidx) => (
            <>
              <TableRow style={{ borderTop: '1px solid black' }} key={pidx}>
                <TableCell style={[pdfStyles.tableCell]}>
                  <Text style={[pdfStyles.textBold]}>No PO: {orderItem?.purchaseOrder?.number}</Text>
                </TableCell>
              </TableRow>
              {orderItem?.isDownPayment ? (
                <>
                  <TableRow
                    style={[pdfStyles.tableRow, { borderBottom: '1px solid #000', borderTop: '0.5px solid #000' }]}
                  >
                    <TableCell style={[pdfStyles.tableCell]}>
                      <Text>Down Payment</Text>
                    </TableCell>
                    <TableCell style={[pdfStyles.tableCell]}>
                      <Text style={pdfStyles.textRight}>
                        {toCurrency(orderItem?.totalAmount, true, purchaseInvoice?.currency?.code)}
                      </Text>
                    </TableCell>
                  </TableRow>
                </>
              ) : (
                <>
                  {orderItem.items?.map((item, index) => (
                    <TableRow
                      key={`${pidx}-${index}`}
                      style={[
                        pdfStyles.tableRow,
                        { borderBottom: index === orderItem.items?.length - 1 ? '1px solid #000' : 'none' },
                        { borderTop: index === 0 ? '1px solid #000' : '0.5px solid #000' }
                      ]}
                    >
                      <TableCell style={[pdfStyles.tableCell]}>
                        <Text>{item?.item?.number}</Text>
                      </TableCell>
                      <TableCell style={[pdfStyles.tableCell]}>
                        <Text style={{ textAlign: 'left' }}>{item?.item?.name}</Text>
                      </TableCell>
                      <TableCell style={[pdfStyles.tableCell]}>
                        <Text style={pdfStyles.textRight}>
                          {toCurrency(item?.pricePerUnit, true, purchaseInvoice?.currency?.code)}
                        </Text>
                      </TableCell>
                      <TableCell style={[pdfStyles.tableCell]}>
                        <Text style={pdfStyles.textRight}>
                          {item?.quantity} {item?.quantityUnit}
                        </Text>
                      </TableCell>
                      <TableCell style={[pdfStyles.tableCell, pdfStyles.flexCol]}>
                        <Text style={pdfStyles.textRight}>
                          {toCurrency(
                            !!item?.taxType
                              ? calculateTax(item.pricePerUnit * item.quantity, item.taxType, item.taxPercentage)
                              : 0,
                            true
                          ) ?? 0}
                        </Text>
                        {item.taxType === 'INCLUDE_TAX' && (
                          <Text style={[pdfStyles.textRight, { fontSize: 6 }]}>*Price Incl Tax</Text>
                        )}
                      </TableCell>
                      <TableCell style={[pdfStyles.tableCell]}>
                        <Text style={pdfStyles.textRight}>
                          {toCurrency(item?.totalAmount, true, purchaseInvoice?.currency?.code)}
                        </Text>
                      </TableCell>
                    </TableRow>
                  ))}
                </>
              )}
            </>
          ))}
        </Table>

        <View style={[pdfStyles.flexRowBetween, { gap: 4 }]}>
          <View style={{ flexGrow: 3 }}>
            {/* Deskripsi dan Total */}
            <View
              style={{
                fontSize: 10,
                display: 'flex',
                alignItems: 'flex-start',
                gap: 4,
                flexDirection: 'row',
                maxWidth: '100%'
              }}
            >
              <Text style={pdfStyles.textNormal}>Terbilang:</Text>
              <Text style={pdfStyles.textNormal}>
                {toTerbilang(purchaseInvoice?.totalAmount)} {purchaseInvoice?.currency?.name.toLowerCase()}
              </Text>
            </View>
          </View>
          <View style={[pdfStyles.totals, { flexGrow: 1 }]}>
            <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
              <TextItemFlex>Sub Total</TextItemFlex>
              <Text style={pdfStyles.textNormal}>:</Text>
              <TextItemFlex end>
                {toCurrency(
                  purchaseInvoice?.orders?.reduce((acc, curr) => acc + curr?.subTotalAmount, 0),
                  true
                )}
              </TextItemFlex>
            </View>

            {!purchaseInvoice?.isDownPayment && (
              <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
                <TextItemFlex>Total Uang Muka</TextItemFlex>
                <Text style={pdfStyles.textNormal}>:</Text>
                <TextItemFlex end>
                  {`- ${toCurrency(
                    purchaseInvoice?.orders?.reduce((acc, item) => acc + item?.downPaymentAmount, 0),
                    true
                  )}`}
                </TextItemFlex>
              </View>
            )}
            <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
              <TextItemFlex>Biaya Lain-lain</TextItemFlex>
              <Text style={pdfStyles.textNormal}>:</Text>
              <TextItemFlex end>
                {toCurrency(purchaseInvoice?.otherAmount ?? 0, true, purchaseInvoice?.currency?.code)}
              </TextItemFlex>
            </View>
            <View>
              <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
                <TextItemFlex
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                >
                  Total Faktur
                </TextItemFlex>
                <Text
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                >
                  :
                </Text>
                <TextItemFlex
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                  end
                >
                  {toCurrency(purchaseInvoice?.totalAmount, true, purchaseInvoice?.currency?.code)}
                </TextItemFlex>
              </View>
            </View>
          </View>
        </View>
        {/* Tanda Tangan */}
        <View style={{ marginVertical: 5 }} />
        {/* <Text style={{ fontSize: 11, paddingLeft: 10, marginBottom: 10 }}>Disetujui Oleh:</Text> */}
        <View style={[pdfStyles.signature, { justifyContent: 'flex-start' }]}>
          <View style={pdfStyles.signatureItem}>
            <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>Dibuat oleh</Text>
            {/* <Image src={poData?.createdByUser?.qr} style={{ width: 40, height: 40 }} /> */}
            <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
            <Text style={pdfStyles.signatureName}>{purchaseInvoice?.createdByUser?.fullName}</Text>
          </View>
          {!isPreview &&
            purchaseInvoice?.approvals?.map((app, index) => (
              <View key={index} style={pdfStyles.signatureItem}>
                <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>{index === 0 ? 'Disetujui oleh' : ''}</Text>
                <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
                <Text style={pdfStyles.signatureName}>{app?.user?.fullName}</Text>
              </View>
            ))}
        </View>
        {isPreview ? (
          <View fixed style={{ top: -220, bottom: 0, left: '20%', right: '50%' }}>
            <Text style={{ color: colors.blueGrey[700], opacity: 0.4, fontWeight: 800, fontSize: 54 }}>
              P R E V I E W
            </Text>
          </View>
        ) : null}
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-end',
            padding: 16,
            width: '100%'
          }}
        >
          {qrCode && <Image src={qrCode} style={{ width: 64, height: 64 }} />}
        </View>
      </Page>
    </Document>
  )
}

export default InvoicePdfDocument
