// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Card, CardContent, Typography } from '@mui/material'

import { FormProvider, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import { useRouter } from '@/routes/hooks'
import { useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useSrList } from '../context/SrListContext'
import { format, formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import ApprovalListCard from '@/pages/material-request/create/components/ApprovalListCard'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import { useSo } from '../context/SoContext'
import { WarehouseItemType } from '@/types/appTypes'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { useAuth } from '@/contexts/AuthContext'
import { useUploadDocument } from '@/api/services/file/mutation'
import AdditionalInfoCard from '../sr-detail/components/AdditionalInfoCard'
import { ServiceOrderItemPayload, ServiceOrderPayload } from '@/types/serviceOrderTypes'
import SegmentCard from '../sr-detail/components/SegmentCard'
import RequestDetailCard from '../sr-detail/components/RequestDetailCard'
import AddPurchaseCard from './components/AddPurchaseCard'

const CreateSoPage = () => {
  const router = useRouter()
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { srData, fetchSrData, fetchSoList } = useSrList()
  const { showLoading, uploadMutate, createSoMutate } = useSo()

  const { mutateAsync: uploadDocumentMutate, isLoading: uploadDocumentLoading } = useUploadDocument()

  const method = useForm<ServiceOrderPayload>({
    mode: 'onChange'
  })

  const { control, handleSubmit, setValue, watch } = method

  const scope = DefaultApprovalScope.ServiceOrder

  const grandTotalWatch = useWatch({
    control,
    name: 'grandTotal',
    defaultValue: 0
  })

  const { data: approverList } = useQuery({
    enabled: !!srData?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, srData?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope,
        siteId: srData?.siteId,
        departmentId: 'null'
        // departmentId: srData?.departmentId ?? userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSubmitHandler: SubmitHandler<ServiceOrderPayload> = ({
    items,
    discountValue,
    shippingCost,
    documentContent,
    documentName,
    ...formInput
  }: ServiceOrderPayload) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat Service Order',
      content:
        'Apakah kamu yakin akan membuat Service Order ini? Pastikan semua detil yang kamu masukkan untuk Service Order ini sudah benar',
      confirmText: 'Buat Service Order',
      onConfirm: () => {
        if (documentContent) {
          uploadDocumentMutate({
            fieldName: `document_${format(new Date(), 'yyyyMMddHHmmss')}`,
            file: documentContent,
            scope: 'public-document',
            fileName: documentName
          })
            .then(uploadResponse => {
              Promise.all(
                items.map(item => {
                  return (item.images?.length ?? 0) > 0
                    ? Promise.all(
                        item.images
                          .filter(image => !!image.content && !image.uploadId)
                          .map(image =>
                            uploadMutate({
                              fieldName: `item_image_${item.itemId}`,
                              file: image.content,
                              scope: 'public-image',
                              fileName: image.fileName
                            })
                          )
                      )
                        .then(values => {
                          const uploadIds = values.map(val => ({
                            uploadId: val.data?.id ?? ''
                          }))
                          return {
                            ...item,
                            images: [
                              ...(item.images ?? [])
                                .filter(image => !image.fileName && !!image.uploadId)
                                .map(image => ({
                                  uploadId: image.uploadId
                                })),
                              ...uploadIds
                            ]
                          }
                        })
                        .catch(error => {
                          const message = error.response?.data?.message
                          if (message) {
                            toast.error(message)
                          } else {
                            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                          }
                          return {} as ServiceOrderItemPayload
                        })
                    : item
                })
              )
                .then(values => {
                  createSoMutate(
                    {
                      ...formInput,
                      approvals: approverList
                        .filter(approver => approver.threshold <= grandTotalWatch)
                        .map(approver => ({
                          userId: approver.user?.id
                        })),
                      items: values
                        .filter(val => !!val.itemId)
                        .map(val => ({
                          itemId: val.itemId,
                          originalItemId: val.originalItemId,
                          quantity: val.quantity,
                          quantityUnit: val.quantityUnit,
                          largeUnitQuantity: val.largeUnitQuantity,
                          serviceName: val.serviceName || '',
                          serialNumberId: val.serialNumberId,
                          serviceDescription: val.serviceDescription || '',
                          serviceFee: val.pricePerUnit,
                          taxType: val.taxType,
                          taxPercentage: TAX_PERCENTAGE,
                          discountType: val.discountType,
                          isDiscountAfterTax: val.isDiscountAfterTax ?? false,
                          note: val.note,
                          images: val.images,
                          ...(val.discountValue && { discountValue: val.discountValue })
                        })),
                      ...(shippingCost && { shippingCost }),
                      ...(discountValue && { discountValue }),
                      documentUploadId: uploadResponse?.data?.id
                    },
                    {
                      onSuccess: () => {
                        fetchSrData()
                        fetchSoList()
                        toast.success('Service Order berhasil dibuat')
                        router.replace('/service-order/list')
                      }
                    }
                  )
                })
                .catch(error => {
                  const message = error.response?.data?.message
                  if (message) {
                    toast.error(message)
                  } else {
                    toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                  }
                })
            })
            .catch(error => {
              const message = error.response?.data?.message
              if (message) {
                toast.error(message)
              } else {
                toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
              }
            })
        } else {
          Promise.all(
            items.map(item => {
              return (item.images?.length ?? 0) > 0
                ? Promise.all(
                    item.images
                      .filter(image => !!image.content && !image.uploadId)
                      .map(image =>
                        uploadMutate({
                          fieldName: `item_image_${item.itemId}`,
                          file: image.content,
                          scope: 'public-image',
                          fileName: image.fileName
                        })
                      )
                  )
                    .then(values => {
                      const uploadIds = values.map(val => ({
                        uploadId: val.data?.id ?? ''
                      }))
                      return {
                        ...item,
                        images: [
                          ...(item.images ?? [])
                            .filter(image => !image.fileName && !!image.uploadId)
                            .map(image => ({
                              uploadId: image.uploadId
                            })),
                          ...uploadIds
                        ]
                      }
                    })
                    .catch(error => {
                      const message = error.response?.data?.message
                      if (message) {
                        toast.error(message)
                      } else {
                        toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                      }
                      return {} as ServiceOrderItemPayload
                    })
                : item
            })
          )
            .then(values => {
              createSoMutate(
                {
                  ...formInput,
                  approvals: approverList
                    .filter(approver => approver.threshold <= grandTotalWatch)
                    .map(approver => ({
                      userId: approver.user?.id
                    })),
                  items: values
                    .filter(val => !!val.itemId)
                    .map(val => ({
                      itemId: val.itemId,
                      originalItemId: val.originalItemId,
                      quantity: val.quantity,
                      quantityUnit: val.quantityUnit,
                      largeUnitQuantity: val.largeUnitQuantity,
                      serviceName: val.serviceName || '',
                      serialNumberId: val.serialNumberId,
                      serviceDescription: val.serviceDescription || '',
                      serviceFee: val.pricePerUnit,
                      taxType: val.taxType,
                      taxPercentage: TAX_PERCENTAGE,
                      discountType: val.discountType,
                      isDiscountAfterTax: val.isDiscountAfterTax ?? false,
                      note: val.note,
                      images: val.images,
                      ...(val.discountValue && { discountValue: val.discountValue })
                    })),
                  ...(shippingCost && { shippingCost }),
                  ...(discountValue && { discountValue })
                },
                {
                  onSuccess: () => {
                    fetchSrData()
                    fetchSoList()
                    toast.success('Service Order berhasil dibuat')
                    router.replace('/service-order/list')
                  }
                }
              )
            })
            .catch(error => {
              const message = error.response?.data?.message
              if (message) {
                toast.error(message)
              } else {
                toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
              }
            })
        }
      }
    })
  }

  useEffect(() => {
    setValue('vendorId', srData?.vendorId ?? null)
    setValue('serviceRequisitionId', srData?.id ?? '')
    setValue(
      'items',
      (srData?.items ?? []).map(item => {
        const remainingQty = item.isLargeUnit ? item.remainingQuantity / item.largeUnitQuantity : item.remainingQuantity
        return { ...item, quantity: remainingQty, originalItemId: item.itemId }
      }) as ServiceOrderItemPayload[]
    )
  }, [srData])

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Service Order</Typography>
            </Link>
            <Link to='/service-order/sr-list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Request Masuk</Typography>
            </Link>
            <Link to={'../..'} replace>
              <Typography color='var(--mui-palette-text-disabled)'>Detil Request</Typography>
            </Link>
            <Typography>Buat Service Order</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Buat Service Order</Typography>
              <Typography>Buat Service Order untuk diajukan kepada Role terkait</Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              <Button
                color='secondary'
                variant='outlined'
                disabled={showLoading || uploadDocumentLoading}
                className='is-full sm:is-auto'
                onClick={() => router.back()}
              >
                Batalkan
              </Button>
              <LoadingButton
                startIcon={<></>}
                disabled={showLoading || uploadDocumentLoading}
                loading={showLoading}
                variant='contained'
                onClick={handleSubmit(onSubmitHandler)}
              >
                Buat Service Order
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <SegmentCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Card>
                <CardContent className='space-y-1'>
                  <Typography variant='h5'>No. SR: {srData?.number}</Typography>
                  <Typography variant='subtitle1'>
                    {formatDate(srData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12}>
              <RequestDetailCard />
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <AddPurchaseCard />
            </Grid>
            {approverList?.length > 0 && (
              <Grid item xs={12}>
                <ApprovalListCard
                  approverList={
                    approverList?.map(approver => ({ ...approver.user, threshold: approver.threshold ?? 0 })) ?? []
                  }
                  scope={DefaultApprovalScope.ServiceOrder}
                  amount={grandTotalWatch}
                />
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreateSoPage
