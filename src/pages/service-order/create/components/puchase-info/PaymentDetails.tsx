import React from 'react'

import { addMonths, formatDate } from 'date-fns'
import { Grid, Typography } from '@mui/material'
import { id } from 'date-fns/locale'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'

interface PaymentDetailsProps {
  paymentDueDate?: string
  paymentMethod: string
  estimatedArrival: string
}

const PaymentDetails: React.FC<PaymentDetailsProps> = ({ paymentDueDate, paymentMethod, estimatedArrival }) => {
  return (
    <div className='flex overflow-hidden flex-col justify-center p-4 w-full text-base font-medium tracking-normal leading-none rounded-lg bg-gray-400 bg-opacity-10 text-gray-600 text-opacity-60 max-md:max-w-full'>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <div className='flex flex-col'>
            <Typography className='text-sm text-gray-400'>Mekanisme Pembayaran</Typography>
            <Typography className='mt-1 text-base font-medium leading-none'>
              {paymentMethodOptions.find(option => option.value === paymentMethod)?.label ?? '-'}
            </Typography>
          </div>
        </Grid>
        {paymentDueDate ? (
          <Grid item xs={12} md={6}>
            <div className='flex flex-col'>
              <Typography className='text-sm text-gray-400'>Tanggal Jatuh Tempo</Typography>
              <Typography className='mt-1 text-base font-medium leading-none'>
                {formatDate(paymentDueDate, 'dd MMM yyyy', {
                  locale: id
                })}
              </Typography>
            </div>
          </Grid>
        ) : null}
        {!!estimatedArrival && (
          <Grid item xs={12} md={6}>
            <div className='flex flex-col'>
              <Typography className='text-sm text-gray-400'>Perkiraan Jasa Selesai</Typography>
              <Typography className='mt-1 text-base font-medium leading-none'>
                {formatDate(estimatedArrival, 'dd MMM yyyy', {
                  locale: id
                })}
              </Typography>
            </div>
          </Grid>
        )}
      </Grid>
    </div>
  )
}

export default PaymentDetails
