// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { But<PERSON>, Chip } from '@mui/material'
import { ThemeColor } from '@/core/types'
import {
  useUpdateSoApprovalStatus,
  useUpdateSoApprover,
  useUpdateSoCancelationStatus
} from '@/api/services/service-order/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'
import { ApproverType } from '@/types/userTypes'
import EditApproverDialog, { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useSo } from '../../context/SoContext'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { ServiceOrderApprovalStatus, ServiceOrderStatus } from '@/types/serviceOrderTypes'

type StatusChipType = {
  label: string
  color: ThemeColor
}

// Vars
export const statusChipValue: { [key: string]: StatusChipType } = {
  WAITING: { label: 'Menunggu', color: 'secondary' },
  PENDING: { label: 'Menunggu', color: 'secondary' },
  APPROVED: { label: 'Disetujui', color: 'success' },
  REJECTED: { label: 'Ditolak', color: 'error' }
}

const ApprovalsCard = ({ isCancelation }: { isCancelation?: boolean }) => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { soData, fetchSoData, fetchSoList, fetchLogList, canUpdate } = useSo()
  const { mutate: updateCancelationStatusMutate } = useUpdateSoCancelationStatus()
  const { mutate: updStatusMutate } = useUpdateSoApprovalStatus()
  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdateSoApprover()

  const updateStatusMutate = isCancelation ? updateCancelationStatusMutate : updStatusMutate

  const [{ open: editApproverOpen, selectedApproval }, setEditApproverModalState] = useState<{
    open: boolean
    selectedApproval?: ApproverType
  }>({
    open: false
  })

  const approvalList = soData?.approvals ?? []

  let ownApprovalIndex = -1
  const ownApproval = soData?.approvals?.find((approval, index) => {
    ownApprovalIndex = index
    return approval.userId === userProfile?.id
  })

  const handleApprove = (id: number) => {
    setConfirmState({
      open: true,
      title: isCancelation ? 'Setujui Pembatalan Service Order' : 'Setujui Service Order',
      content: `Apakah kamu yakin akan menyetujui ${isCancelation ? 'Pengajuan Pembatalan Service Order' : 'Service Order'} ini? Action ini tidak bisa diubah`,
      confirmText: 'Setujui',
      onConfirm: () => {
        updateStatusMutate(
          {
            soId: soData?.id,
            approvalId: id,
            status: ServiceOrderApprovalStatus.APPROVED
          },
          {
            onSuccess: () => {
              toast.success(
                isCancelation
                  ? 'Pengajuan Pembatalan Service Order berhasil disetujui'
                  : 'Service Order berhasil disetujui'
              )
              fetchSoData()
              fetchLogList()
              fetchSoList()
            }
          }
        )
      }
    })
  }

  const handleReject = (id: number) => {
    setConfirmState({
      open: true,
      title: isCancelation ? 'Tolak Pembatalan Service Order' : 'Tolak Service Order',
      content: `Apakah kamu yakin akan menolak ${isCancelation ? 'Pengajuan Pembatalan Service Order' : 'Service Order'} ini? Action ini tidak bisa diubah`,
      confirmText: 'Tolak',
      onConfirm: () => {
        updateStatusMutate(
          {
            soId: soData?.id,
            approvalId: id,
            status: ServiceOrderApprovalStatus.REJECTED
          },
          {
            onSuccess: () => {
              toast.success(
                isCancelation ? 'Pengajuan Pembatalan Service Order berhasil ditolak' : 'SO berhasil ditolak'
              )
              fetchSoData()
              fetchLogList()
              fetchSoList()
            }
          }
        )
      },
      confirmColor: 'error'
    })
  }

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        soId: soData?.id,
        approvalId: selectedApproval?.id,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchSoData()
          fetchLogList()
          setEditApproverModalState({ open: false })
        }
      }
    )
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>{isCancelation ? 'Persetujuan Pembatalan' : 'Pengajuan Persetujuan'}</Typography>
          </div>
          <div className='flex flex-col gap-4'>
            {approvalList.map((approval, index) => {
              const statusValue = statusChipValue[approval.status]
              return (
                <div
                  key={approval.id}
                  className='rounded-lg border border-[#4c4e64]/22 p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'
                >
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {approval.user?.fullName}
                    </p>
                    {ownApproval?.status === ServiceOrderApprovalStatus.WAITING &&
                    approval.status === ServiceOrderApprovalStatus.WAITING &&
                    soData?.status !== ServiceOrderStatus.CANCELED ? (
                      <div className='flex gap-2 items-center self-center'>
                        <Button
                          variant='contained'
                          size='small'
                          color='error'
                          onClick={() => handleReject(approval.id)}
                        >
                          Tolak
                        </Button>
                        <Button variant='contained' size='small' onClick={() => handleApprove(approval.id)}>
                          Setujui
                        </Button>
                      </div>
                    ) : (
                      <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
                    )}
                  </div>
                  <div className='flex justify-between items-start w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>{approval.user?.title}</small>
                    </label>
                    {approval.respondedAt ? (
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>
                          {formatDate(approval.respondedAt, 'dd MMM yyyy, HH:mm', { locale: id })}
                        </small>
                      </label>
                    ) : null}
                  </div>
                  {!!approval?.rejectionNote && (
                    <div>
                      <small>Catatan:</small>
                      <Typography>{approval?.rejectionNote}</Typography>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {editApproverOpen ? (
        <EditApproverDialog
          open={editApproverOpen}
          setOpen={open =>
            setEditApproverModalState(current => ({
              open: open,
              selectedApproval: open ? current.selectedApproval : undefined
            }))
          }
          selectedApproval={selectedApproval}
          scope={DefaultApprovalScope.ServiceOrder}
          onSubmit={handleUpdateApprover}
          isLoading={updateApproverLoading}
          approvalList={approvalList}
        />
      ) : null}
    </>
  )
}

export default ApprovalsCard
