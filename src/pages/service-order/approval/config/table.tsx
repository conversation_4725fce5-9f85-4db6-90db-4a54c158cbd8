import { StatusChipColorType } from '@/types/appTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { serviceOrderStatusOptions } from '../../list/config/utils'
import { statusChipValue } from '../../approval-detail/components/ApprovalsCard'
import { MrPriority, mrPriorityOptions } from '@/pages/material-request/create/config/enum'
import { MrStatus } from '@/types/mrTypes'
import { ServiceOrder, ServiceOrderStatus } from '@/types/serviceOrderTypes'
import { requestTypeOptions } from '@/pages/repair-and-maintenance/wo/create-wo-sr/config/utils'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [ServiceOrderStatus.PROCESSED]: { color: 'warning' },
  [ServiceOrderStatus.APPROVED]: { color: 'success' },
  [ServiceOrderStatus.CANCELED]: { color: 'error' },
  [ServiceOrderStatus.REJECTED]: { color: 'error' },
  [ServiceOrderStatus.CANCEL_REQUESTED]: { color: 'error' },
  [ServiceOrderStatus.CLOSED]: { color: 'info' }
}

type SoTypeWithAction = ServiceOrder & {
  action?: string
  isRead?: boolean
}

type RowActionType = {
  showDetail: (id: string, isCancelation?: boolean) => void
}

// Column Definitions
const columnHelper = createColumnHelper<SoTypeWithAction>()

export const tableColumns = (rowAction: RowActionType, userId?: string): any[] => [
  columnHelper.accessor('number', {
    header: 'No. SO',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() =>
          rowAction.showDetail(row.original.id, row.original.status === ServiceOrderStatus.CANCEL_REQUESTED)
        }
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => {
      const docStatus = row.original.status
      const ownApproval = row.original.approvals?.find(approval => approval.userId === userId)
      return docStatus !== ServiceOrderStatus.CANCELED ? (
        <Chip
          label={statusChipValue[ownApproval?.status]?.label}
          color={statusChipValue[ownApproval?.status]?.color}
          variant='tonal'
          size='small'
        />
      ) : (
        <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
      )
    }
  }),
  // columnHelper.accessor('itemsCount', {
  //   header: 'Item',
  //   cell: ({ row }) => <Typography>{row.original.itemsCount}</Typography>
  // }),
  columnHelper.accessor('site', {
    header: 'Workshop',
    cell: ({ row }) => <Typography>{row.original.site?.name}</Typography>
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => <Typography>{row.original.department?.name}</Typography>
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = mrPriorityOptions.find(option => option.value === String(row.original.priority ?? MrPriority.P4))
      return (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('type', {
    header: 'Jenis Request',
    cell: ({ row }) => (
      <Typography>{requestTypeOptions.find(option => option.value === row.original.type)?.label}</Typography>
    )
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('createdByUser.fullName', {
    header: 'Dibuat Oleh',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.createdByUser?.fullName}</Typography>
        <Typography variant='caption'>{row.original.createdByUser?.title}</Typography>
      </div>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton
          size='small'
          onClick={() =>
            rowAction.showDetail(row.original.id, row.original.status === ServiceOrderStatus.CANCEL_REQUESTED)
          }
        >
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
