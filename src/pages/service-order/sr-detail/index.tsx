import { useState } from 'react'
// MUI Imports
import Grid from '@mui/material/Grid'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { useSrList } from '../context/SrListContext'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import { ServiceRequisitionStatus } from '@/types/serviceRequisitionsTypes'
import ActivityLogCard from './components/ActivityLogCard'
import SelectItemDialog from '@/components/dialogs/select-item-dialog'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import SoList from './components/SoList'
import AdditionalInfoCard from './components/AdditionalInfoCard'
import UnitCard from './components/UnitCard'
import RequestDetailCard from './components/RequestDetailCard'
import SegmentCard from './components/SegmentCard'
import { useRouter } from '@/routes/hooks'
import CompanyQueryMethods, { VENDOR_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'

const SrDetailPage = () => {
  const [selectItemDialogOpen, setSelectItemDialogOpen] = useState(false)
  const { srData, logList } = useSrList()

  const { data: vendorData } = useQuery({
    enabled: !!srData?.vendorId,
    queryKey: [VENDOR_QUERY_KEY, srData?.vendorId],
    queryFn: () => CompanyQueryMethods.getVendor(srData?.vendorId)
  })

  const router = useRouter()

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Service Order</Typography>
            </Link>
            <Link to='/service-order/sr-list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Request Masuk</Typography>
            </Link>
            <Typography>Detil Request</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>No. SR: {srData?.number}</Typography>
                {srData?.status !== ServiceRequisitionStatus.CLOSED ? (
                  <Chip
                    label={
                      srData?.serviceOrdersCount > 0
                        ? `${srData?.serviceOrdersCount} Service Order Terbuat`
                        : 'Service Order belum dibuat'
                    }
                    color={srData?.serviceOrdersCount <= 0 ? 'default' : 'success'}
                    variant='tonal'
                    size='small'
                  />
                ) : (
                  <Chip label='Service Request sudah ditutup' color='success' variant='tonal' size='small' />
                )}
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(srData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 is-full sm:is-auto'>
              {!srData?.serviceOrdersCount ? (
                <Button
                  variant='contained'
                  className='is-full sm:is-auto'
                  onClick={() => router.push(`/service-order/sr-list/${srData?.id}/so/create`)}
                >
                  Buat Service Order
                </Button>
              ) : null}
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <SoList />
        </Grid>
        <Grid item xs={12}>
          <SegmentCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DocNumberCard
                docType='WO'
                warehouseDoc={srData?.workOrder}
                note={`Segment No. ${srData?.workOrderSegment?.number}`}
              />
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard />
            </Grid>
            <Grid item xs={12}>
              <UnitCard />
            </Grid>
            <Grid item xs={12}>
              <OwnerCard user={srData?.createdByUser} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {srData?.approvals ? (
              <Grid item xs={12}>
                <ApprovalDetailCard approvalList={srData?.approvals ?? []} />
              </Grid>
            ) : null}
            <Grid item xs={12}>
              <RequestDetailCard vendorData={vendorData} />
            </Grid>
            <Grid item xs={12}>
              <ActivityLogCard logList={logList} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {selectItemDialogOpen ? <SelectItemDialog open={selectItemDialogOpen} setOpen={setSelectItemDialogOpen} /> : null}
    </>
  )
}

export default SrDetailPage
