import { DEFAULT_DEPARTMENT } from '@/data/default/department'
import { requestTypeOptions } from '@/pages/repair-and-maintenance/wo/create-wo-sr/config/utils'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'
import { StatusChipColorType } from '@/types/appTypes'
import {
  ServiceRequisition,
  ServiceRequisitionPriority,
  ServiceRequisitionStatus
} from '@/types/serviceRequisitionsTypes'
import { Checkbox, Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [ServiceRequisitionStatus.PROCESSED]: { color: 'secondary' },
  [ServiceRequisitionStatus.APPROVED]: { color: 'success' },
  [ServiceRequisitionStatus.REJECTED]: { color: 'error' },
  [ServiceRequisitionStatus.CLOSED]: { color: 'success' }
}

type SrTypeWithAction = ServiceRequisition & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<SrTypeWithAction>()

export const tableColumns = (rowAction: RowActionType) => [
  // {
  //   id: 'select',
  //   header: ({ table }) => (
  //     <Checkbox
  //       {...{
  //         checked: table.getIsAllRowsSelected(),
  //         indeterminate: table.getIsSomeRowsSelected(),
  //         onChange: table.getToggleAllRowsSelectedHandler()
  //       }}
  //     />
  //   ),
  //   cell: ({ row }) => (
  //     <Checkbox
  //       {...{
  //         checked: row.getIsSelected(),
  //         disabled: !row.getCanSelect(),
  //         indeterminate: row.getIsSomeSelected(),
  //         onChange: row.getToggleSelectedHandler()
  //       }}
  //     />
  //   )
  // },
  columnHelper.accessor('number', {
    header: 'No. SR',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('serviceOrdersCount', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={
          row.original.serviceOrdersCount > 0
            ? `${row.original.serviceOrdersCount} Service Order Terbuat`
            : 'Service Order belum dibuat'
        }
        color={row.original.serviceOrdersCount <= 0 ? 'default' : 'success'}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('site', {
    header: 'Workshop',
    cell: ({ row }) => <Typography>{row.original.site?.name}</Typography>
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => <Typography>{row.original.department?.name ?? DEFAULT_DEPARTMENT.name}</Typography>
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = srPriorityOptions.find(
        option => option.value === String(row.original.priority ?? ServiceRequisitionPriority.P4)
      )
      return (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('type', {
    header: 'Jenis Request',
    cell: ({ row }) => (
      <Typography>{requestTypeOptions.find(option => option.value === row.original.type)?.label}</Typography>
    )
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal DIBUAT',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('createdByUser.fullName', {
    header: 'Dibuat Oleh',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.createdByUser?.fullName}</Typography>
        <Typography variant='caption'>{row.original.createdByUser?.title}</Typography>
      </div>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
