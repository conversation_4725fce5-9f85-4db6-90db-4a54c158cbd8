// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrumbs, Button, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ActivityLogCard from './components/ActivityLogCard'
import { mtStatusOptions } from '../../config/options'
import { useMt } from '../../context/MtContext'
import { statusChipColor } from '../config/table'
import InfoCard from './components/InfoCard'
import TableCard from '../../component/table'
import { MaterialTransferStatus, UserRole } from '../../config/enum'
import MrNumberCard from '@/pages/material-request/mr-in/component/MrNumberCard'
import { useUpdateMtApprover } from '@/api/services/mt/mutation'
import { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { toast } from 'react-toastify'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import ItemListCard from './components/ItemListCard'
import ApprovalDetailCard from '../components/ApprovalDetailCard'
import UnitCard from '@/pages/material-request/create/components/UnitCard'
import SjListCard from '../../create-sj/components/SjListCard'

const MtCreatedDetailPage = () => {
  const { mtData, logList, mrData, handleCancelMt, handleCloseMt, canUpdate, canRemove, fetchMtData, fetchLogList } =
    useMt()

  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdateMtApprover()

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        mtId: mtData?.id,
        approvalId: formData.approvalId,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchMtData()
          fetchLogList()
        },
        onError: error => {
          const message = error?.message
          if (message) {
            toast.error(message)
          } else {
            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
          }
        }
      }
    )
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Material Transfer</Typography>
          </Link>
          <Link to='/mt/created' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Material Transfer Terbuat</Typography>
          </Link>
          <Typography>Detil Material Transfer</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>NO. MT: {mtData?.number}</Typography>
              <Chip
                label={mtStatusOptions.find(option => option.value === mtData?.status)?.label}
                color={statusChipColor[mtData?.status]?.color}
                variant='tonal'
                size='small'
              />
              <Chip
                label={mtData?.remainingQuantity > 0 ? 'Belum Terpenuhi' : 'Full Supply'}
                color={mtData?.remainingQuantity > 0 ? 'warning' : 'success'}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(mtData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button> */}
            </div>
            {mtData?.status === MaterialTransferStatus.PROCESSED && canRemove && (
              <Button color='error' variant='contained' className='is-full sm:is-auto' onClick={handleCancelMt}>
                Batalkan MT
              </Button>
            )}
            {mtData?.status === MaterialTransferStatus.APPROVED && canUpdate && (
              <Button color='warning' variant='contained' className='is-full sm:is-auto' onClick={handleCloseMt}>
                Tutup MT
              </Button>
            )}
          </div>
        </div>
      </Grid>
      {mtData?.items ? (
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
      ) : null}
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DocNumberCard warehouseDoc={mrData} docType='MR' />
          </Grid>
          <Grid item xs={12}>
            <InfoCard />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={logList} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {/* <Grid item xs={12}>
            <TableCard />
          </Grid> */}
          {mrData?.unit && (
            <Grid item xs={12}>
              <UnitCard warehouseData={mrData} />
            </Grid>
          )}
          <Grid item xs={12}>
            <SjListCard />
          </Grid>
          {(mtData?.approvals?.length ?? 0) > 0 ? (
            <>
              {(mtData?.approvals?.filter(approval => approval.role === UserRole.REQUESTER)?.length ?? 0) > 0 ? (
                <Grid item xs={12}>
                  <ApprovalDetailCard
                    approvalList={mtData?.approvals?.filter(approval => approval.role === UserRole.REQUESTER) ?? []}
                    handleUpdateApprover={handleUpdateApprover}
                    updateApproverLoading={updateApproverLoading}
                    role={UserRole.REQUESTER}
                  />
                </Grid>
              ) : null}
              {(mtData?.approvals?.filter(approval => approval.role === UserRole.REQUESTED)?.length ?? 0) > 0 ? (
                <Grid item xs={12}>
                  <ApprovalDetailCard
                    approvalList={mtData?.approvals?.filter(approval => approval.role === UserRole.REQUESTED) ?? []}
                    handleUpdateApprover={handleUpdateApprover}
                    updateApproverLoading={updateApproverLoading}
                    role={UserRole.REQUESTED}
                  />
                </Grid>
              ) : null}
            </>
          ) : null}
          <Grid item xs={12}>
            <OwnerCard user={mtData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default MtCreatedDetailPage
