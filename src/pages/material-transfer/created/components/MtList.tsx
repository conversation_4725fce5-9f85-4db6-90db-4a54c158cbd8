import { useEffect, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useRouter } from '@/routes/hooks'
import { useMt } from '../../context/MtContext'
import { mtStatusOptions } from '../../config/options'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { mrPriorityOptions } from '@/pages/material-request/create/config/enum'
import { useAuth } from '@/contexts/AuthContext'

const MtList = () => {
  const router = useRouter()
  const { ownSiteList } = useAuth()
  const {
    mtListResponse: { items: mtList, totalItems, totalPages, limit: limitItems, page: pageItems },
    mtListParams,
    setMtListParams,
    setSelectedMt,
    setPartialMtListParams,
    siteList
  } = useMt()

  const { page, search, status, startDate, endDate, requestedSiteIds, priority, departmentId, limit } = mtListParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: mtList,
    columns: tableColumns({
      showDetail: (id, isCancelation) => {
        setSelectedMt({ mtId: id, isCancelation })
        router.push(`/mt/created/${id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({
    date,
    status,
    priority,
    department,
    requestedSiteIds: requestedSiteIdList
  }: FilterValues) => {
    setMtListParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status.length > 0 ? status[0] : undefined,
        priority: priority.length > 0 ? priority[0] : undefined,
        // departmentId: department.length > 0 ? department[0] : undefined,
        requestedSiteIds: requestedSiteIdList.length > 0 ? requestedSiteIdList[0] : undefined
      }
    })
  }

  useEffect(() => {
    setMtListParams({
      ...mtListParams,
      limit: 10,
      page: 1,
      siteIds: ownSiteList.map(site => site.id).join(',')
    })
  }, [ownSiteList])

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      status: {
        options: mtStatusOptions,
        values: status ? [status] : []
      },
      priority: {
        options: mrPriorityOptions,
        values: priority ? [priority] : []
      },
      requestedSiteIds: {
        options: siteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: requestedSiteIds ? [requestedSiteIds] : []
      }
      // department: {
      //   options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
      //   values: departmentId ? [departmentId] : []
      // }
    })
  }, [mtListParams])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setMtListParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari MT'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada MT</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua MT yang telah dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setMtListParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialMtListParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setMtListParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialMtListParams('page', pageIndex)}
      />
    </Card>
  )
}

export default MtList
