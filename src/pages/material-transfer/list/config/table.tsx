import { StatusChipColorType } from '@/types/appTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { mtStatusOptions } from '../../config/options'
import { MaterialTransferStatus } from '../../config/enum'
import { MtType } from '../../config/types'
import { MrPriority, mrPriorityOptions } from '@/pages/material-request/create/config/enum'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [MaterialTransferStatus.PROCESSED]: { color: 'warning' },
  [MaterialTransferStatus.APPROVED]: { color: 'success' },
  [MaterialTransferStatus.CANCELED]: { color: 'error' },
  [MaterialTransferStatus.REJECTED]: { color: 'error' },
  [MaterialTransferStatus.CANCEL_REQUESTED]: { color: 'error' },
  [MaterialTransferStatus.CLOSED]: { color: 'info' }
}

type MtTypeWithAction = MtType & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string, isCancelation?: boolean) => void
}

// Column Definitions
const columnHelper = createColumnHelper<MtTypeWithAction>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. MT',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('deliveryNotesCount', {
    header: 'Status Surat Jalan',
    cell: ({ row }) => {
      const deliveryNotesCount = row.original.deliveryNotesCount ?? 0
      return (
        <Chip
          label={deliveryNotesCount > 0 ? `${deliveryNotesCount} SJ dibuat` : 'SJ belum dibuat'}
          color={deliveryNotesCount > 0 ? 'success' : 'default'}
          variant='tonal'
          size='small'
        />
      )
    }
  }),
  // columnHelper.accessor('itemsCount', {
  //   header: 'Item',
  //   cell: ({ row }) => <Typography>{row.original.itemsCount} Item</Typography>
  // }),
  columnHelper.accessor('requesterSite', {
    header: 'Permintaan Dari',
    cell: ({ row }) => <Typography>{row.original.requesterSite?.name}</Typography>
  }),
  columnHelper.accessor('requesterSite', {
    header: 'Permintaan Ke',
    cell: ({ row }) => <Typography>{row.original.requestedSite?.name}</Typography>
  }),
  columnHelper.accessor('remainingQuantity', {
    header: 'Status Transfer',
    cell: ({ row }) => (
      <Chip
        label={row.original?.remainingQuantity > 0 ? 'Belum Terpenuhi' : 'Full Supply'}
        color={row.original?.remainingQuantity > 0 ? 'warning' : 'success'}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => <Typography>{row.original.department?.name}</Typography>
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = mrPriorityOptions.find(option => option.value === String(row.original.priority ?? MrPriority.P4))
      return (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal Dibuat',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton
          size='small'
          onClick={() =>
            rowAction.showDetail(row.original.id, row.original.status === MaterialTransferStatus.CANCEL_REQUESTED)
          }
        >
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
