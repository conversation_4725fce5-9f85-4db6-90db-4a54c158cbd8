import Grid from '@mui/material/Grid'
import { Breadcrum<PERSON>, Card, CardContent, Chip, Typography } from '@mui/material'
import { useMt } from '../context/MtContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import InfoCard from '../created/detail/components/InfoCard'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import TableCard from './components/table'
import SjListCard from './components/SjListCard'
import ActivityLogCard from '../approval/detail/components/ActivityLogCard'
import { Link } from 'react-router-dom'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'

const CreateSjPage = () => {
  const { mtData, logList, mrData } = useMt()

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Material Transfer</Typography>
          </Link>
          <Link to='/mt/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Material Transfer Masuk</Typography>
          </Link>
          <Typography>Detil Material Transfer</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>NO. MT: {mtData?.number}</Typography>
              <Chip
                label={mtData?.deliveryNotesCount > 0 ? `${mtData?.deliveryNotesCount} SJ dibuat` : 'SJ belum dibuat'}
                color={mtData?.deliveryNotesCount > 0 ? 'success' : 'default'}
                variant='tonal'
                size='small'
              />
              <Chip
                label={mtData?.remainingQuantity > 0 ? 'Belum Terpenuhi' : 'Full Supply'}
                color={mtData?.remainingQuantity > 0 ? 'warning' : 'success'}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(mtData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={5}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DocNumberCard warehouseDoc={mrData} docType='MR' />
          </Grid>
          <Grid item xs={12}>
            <InfoCard />
          </Grid>
          {logList?.length > 0 && (
            <Grid item xs={12}>
              <ActivityLogCard logList={logList} />
            </Grid>
          )}
        </Grid>
      </Grid>
      <Grid item xs={12} md={7}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <TableCard />
          </Grid>
          <Grid item xs={12}>
            <SjListCard />
          </Grid>
          <Grid item xs={12}>
            <OwnerCard user={mtData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default CreateSjPage
