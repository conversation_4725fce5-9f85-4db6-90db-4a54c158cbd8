import { createContext, ReactN<PERSON>, useContext, useEffect, useState } from 'react'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  UseMutateAsyncFunction,
  UseMutateFunction,
  useQuery
} from '@tanstack/react-query'
import usePartialState from '@/core/hooks/usePartialState'
import { ApiResponse, ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { useParams } from 'react-router-dom'
import { usePathname } from '@/routes/hooks'
import { useAuth } from '@/contexts/AuthContext'
import { useUploadImage } from '@/api/services/file/mutation'
import { FileType } from '@/types/fileTypes'
import { UploadPayload } from '@/types/payload'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { MtLogType, MtParams, MtPayload, MtType, SjParams, SjType } from '../config/types'
import { useAddMt, useCloseMt } from '@/api/services/mt/mutation'
import MtQueryMethods, {
  MT_LIST_QUERY_KEY,
  MT_LOG_LIST_QUERY_KEY,
  MT_QUERY_KEY,
  SJ_LIST_QUERY_KEY
} from '@/api/services/mt/query'
import { MrType, MrUserStatus } from '@/types/mrTypes'
import MrQueryMethods, { MR_QUERY_KEY } from '@/api/services/mr/query'
import {
  DeliveryNoteStatus,
  DeliveryNoteType,
  MaterialTransferLogType,
  MaterialTransferStatus,
  MaterialTransferType
} from '../config/enum'
import CancelMtDialog from '@/components/dialogs/cancel-mt-dialog'
import CompanyQueryMethods, { SITE_LIST_QUERY_KEY } from '@/api/services/company/query'
import { SiteType } from '@/types/companyTypes'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'

interface MtContextProps {
  showLoading: boolean
  mtListResponse: ListResponse<MtType>
  mtListParams: MtParams
  setMtListParams: React.Dispatch<React.SetStateAction<MtParams>>
  fetchMtList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<MtType>, unknown>>
  mtData: MtType
  clearMtData: () => void
  selectedMtId?: string
  setSelectedMt: React.Dispatch<
    React.SetStateAction<{
      mtId?: string
      isCancelation?: boolean
    }>
  >
  setPartialMtListParams: (fieldName: string, value: any) => void
  fetchMtData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<MtType, unknown>>
  uploadMutate: UseMutateAsyncFunction<ApiResponse<FileType>, Error, UploadPayload, void>
  createMtMutate: UseMutateFunction<ApiResponse<MtType>, Error, MtPayload, void>
  logList: MtLogType[]
  fetchLogList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<MtLogType[], unknown>>
  refreshData: () => void
  mrData: MrType
  canUpdate: boolean
  canCreate: boolean
  sjList: SjType[]
  fetchSjList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<SjType[], unknown>>
  handleCancelMt: () => void
  handleCloseMt: () => void
  canRemove: boolean
  siteList: SiteType[]
}

export const MtContext = createContext<MtContextProps>({} as MtContextProps)

interface MtContextProviderProps {
  children: ReactNode
}

export const useMt = () => {
  return useContext(MtContext)
}

export function MtContextProvider({ children }: MtContextProviderProps) {
  const pathname = usePathname()
  const { accountPermissions, userProfile } = useAuth()
  const { mtId, mrId } = useParams()
  const { setConfirmState } = useMenu()
  const [{ mtId: selectedMtId, isCancelation }, setSelectedMt] = useState<{ mtId?: string; isCancelation?: boolean }>({
    mtId
  })

  const [cancelMtDialogOpen, setCancelMtDialogOpen] = useState(false)

  const [mtListParams, setPartialMtListParams, setMtListParams] = usePartialState<MtParams>({
    limit: 10,
    page: 1
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: createMtMutate, isLoading: createMtLoading } = useAddMt()
  const { mutate: closeMtMutate, isLoading: closeMtLoading } = useCloseMt()

  const isByMePage = pathname.includes('created')
  const isApprovalPage = pathname.includes('approval')
  const isReceivePage = pathname.includes('receive') || pathname.includes('list')
  const canCreate = accountPermissions.includes(`${DefaultApprovalScope.MaterialTransfer}.create`)
  const canUpdate = accountPermissions.includes(`${DefaultApprovalScope.MaterialTransfer}.update`)

  const ownSiteList = userProfile?.sites ?? []

  const {
    data: mtData,
    refetch: fetchMtData,
    isFetching: fetchMtDataLoading,
    remove: removeMtData
  } = useQuery({
    enabled: !!selectedMtId,
    queryKey: [MT_QUERY_KEY, selectedMtId, isCancelation],
    queryFn: () => MtQueryMethods.getMt(selectedMtId, isCancelation)
  })

  const canRemove = mtData?.isEditable && canCreate

  const {
    data: logList,
    refetch: fetchLogList,
    remove: removeMtLogList
  } = useQuery({
    enabled: !!selectedMtId,
    queryKey: [MT_LOG_LIST_QUERY_KEY, selectedMtId, isReceivePage],
    queryFn: () =>
      MtQueryMethods.getMtLogList(selectedMtId, {
        limit: 10000,
        type: isReceivePage ? MaterialTransferLogType.DELIVERY_NOTE : ''
      })
  })

  const {
    data: mtListResponse,
    refetch: fetchMtList,
    isFetching: fetchMtsLoading
  } = useQuery({
    enabled: !!userProfile && (!!mtListParams?.siteIds || isReceivePage),
    queryKey: [MT_LIST_QUERY_KEY, JSON.stringify(mtListParams), isByMePage, isReceivePage, pathname],
    queryFn: () => {
      const { search, status, startDate, endDate, userStatus, requestedSiteIds, requesterSiteIds, ...params } =
        mtListParams
      const payload = {
        ...(search && { search }),
        ...(status && !isApprovalPage && { status }),
        ...(userStatus && { userStatus }),
        ...(!!startDate && !!endDate && { startDate, endDate }),
        ...(!!requestedSiteIds && { requestedSiteIds }),
        ...(!!requesterSiteIds && { requesterSiteIds }),
        ...params,
        types: `${MaterialTransferType.IN_SITE},${MaterialTransferType.S2S_TRANSFER}`,
        ...(isReceivePage && { status: MaterialTransferStatus.APPROVED, types: MaterialTransferType.S2S_TRANSFER }),
        ...(pathname.includes('receive') && {
          isHasDeliveryNote: true,
          requesterSiteIds: requesterSiteIds?.length > 0 ? requesterSiteIds : ownSiteList.map(site => site.id).join(',')
        }),
        ...(pathname.includes('list') && {
          requestedSiteIds: requestedSiteIds?.length > 0 ? requestedSiteIds : ownSiteList.map(site => site.id).join(',')
        })
      }
      if (isByMePage) {
        return MtQueryMethods.getMtList(payload)
      } else if (isApprovalPage) {
        return MtQueryMethods.getToMeMtList(payload)
      } else {
        return MtQueryMethods.getMtList(payload)
      }
    },
    placeholderData: defaultListData as ListResponse<MtType>
  })

  const {
    data: mrData,
    refetch: fetchMrData,
    isFetching: fetchMrDataLoading,
    remove: removeMrData
  } = useQuery({
    enabled: !!mrId || !!mtData?.materialRequestId,
    queryKey: [MR_QUERY_KEY, mrId],
    queryFn: () => MrQueryMethods.getMr(mrId ?? mtData?.materialRequestId)
  })

  const {
    data: sjList,
    refetch: fetchSjList,
    isFetching: fetchSjListLoading,
    remove: removeSjList
  } = useQuery({
    enabled: !!mtId,
    queryKey: [SJ_LIST_QUERY_KEY, mtId, pathname],
    queryFn: async () => {
      const list = await MtQueryMethods.getSjList(mtId, {
        limit: Number.MAX_SAFE_INTEGER,
        type: DeliveryNoteType.DELIVERY,
        ...(pathname.includes('receive') && { status: DeliveryNoteStatus.PROCESSED })
      })
      return Promise.all(
        list.map(async sj => {
          return await MtQueryMethods.getSj(sj?.id, mtId)
        })
      )
    },
    placeholderData: [],
    cacheTime: 0
  })

  const {
    data: { items: siteList }
  } = useQuery({
    queryKey: [SITE_LIST_QUERY_KEY],
    queryFn: () => {
      return CompanyQueryMethods.getSiteList({
        limit: Number.MAX_SAFE_INTEGER
      })
    },
    placeholderData: defaultListData as ListResponse<SiteType>
  })

  const clearMtData = () => {
    removeMtData()
    removeMtLogList()
    setSelectedMt({ mtId: undefined })
  }

  const refreshData = () => {
    fetchMtData()
    fetchLogList()
    fetchMtList()
  }

  const handleCancelMt = () => {
    setCancelMtDialogOpen(true)
  }

  const handleCloseMt = () => {
    setConfirmState({
      open: true,
      title: 'Tutup MT',
      content: 'Apakah kamu yakin akan menutup MT ini? Action ini tidak bisa diubah',
      confirmText: 'Tutup MT',
      confirmColor: 'primary',
      onConfirm: () => {
        closeMtMutate(mtData?.id, {
          onSuccess: () => {
            fetchMtData()
            toast.success('MT berhasil ditutup')
            setTimeout(() => {
              fetchLogList()
            }, 500)
          }
        })
      }
    })
  }

  useEffect(() => {
    setSelectedMt(curr => ({ ...curr, mtId }))
  }, [mtId])

  const value = {
    mtListResponse: mtListResponse ?? defaultListData,
    showLoading: uploadLoading || createMtLoading,
    fetchMtList,
    mtListParams,
    setPartialMtListParams,
    setMtListParams,
    mtData,
    selectedMtId,
    setSelectedMt,
    clearMtData,
    fetchMtData,
    uploadMutate,
    createMtMutate,
    logList,
    fetchLogList,
    refreshData,
    mrData,
    canUpdate,
    canCreate,
    sjList,
    fetchSjList,
    handleCancelMt,
    handleCloseMt,
    canRemove,
    siteList: siteList ?? []
  }

  return (
    <MtContext.Provider value={value}>
      <>
        {children}
        {cancelMtDialogOpen ? <CancelMtDialog open={cancelMtDialogOpen} setOpen={setCancelMtDialogOpen} /> : null}
      </>
    </MtContext.Provider>
  )
}
