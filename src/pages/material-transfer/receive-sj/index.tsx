import Grid from '@mui/material/Grid'
import { <PERSON>, Card<PERSON>ontent, CardHeader, Chip, Typography } from '@mui/material'
import { useMt } from '../context/MtContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import InfoCard from '../created/detail/components/InfoCard'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ReceiveTableCard from './components/table'
import ActivityLogCard from '../approval/detail/components/ActivityLogCard'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { FormProvider, SubmitHandler, useForm, useWatch } from 'react-hook-form'
import { SjReceiptPayloadList } from '../config/types'
import { useReceiveSj } from '@/api/services/mt/mutation'
import { zodResolver } from '@hookform/resolvers/zod'
import { receiveSjPayloadListSchema } from './config/schema'
import { toast } from 'react-toastify'
import MrNumberCard from '@/pages/material-request/mr-in/component/MrNumberCard'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import TableCard from '../component/table'
import SjListCard from './components/SjListCard'

const ReceiveSjPage = () => {
  const { setConfirmState } = useMenu()

  const { mutate: receiveSjMutate, isLoading } = useReceiveSj()

  const { mtData, logList, sjList, fetchMtData, fetchLogList, fetchSjList, mrData } = useMt()

  const methods = useForm<SjReceiptPayloadList>({
    resolver: zodResolver(receiveSjPayloadListSchema),
    mode: 'onChange',
    defaultValues: {
      payloads: []
    }
  })

  const { handleSubmit, control, reset } = methods

  const payloadListWatch = useWatch({
    control,
    name: `payloads`,
    defaultValue: []
  })

  const onSubmitHandler: SubmitHandler<SjReceiptPayloadList> = (formInput: SjReceiptPayloadList) => {
    const activePayload = formInput.payloads?.filter(payload =>
      payload.items?.some(item => item.isActive && (item.receivedQuantity ?? 0) > 0)
    )
    if (activePayload.length > 0) {
      setConfirmState({
        open: true,
        title: `Konfirmasi dan Perbarui Stok`,
        content:
          'Pastikan quantity yang kamu masukkan sudah sesuai dengan yang kamu terima. Apakah kamu yakin akan mengkonfirmasi penerimaan barang dan memperbarui stok? Action ini tidak dapat diubah',
        confirmText: `Konfirmasi`,
        onConfirm: () => {
          activePayload.forEach(payload => {
            receiveSjMutate([mtData?.id, payload.sjId, payload], {
              onSuccess: () => {
                reset({
                  payloads: []
                })
                fetchLogList()
                fetchMtData()
                fetchSjList()
                toast.success(`Stok barang berhasil diperbarui`)
              }
            })
          })
        }
      })
    }
  }

  return (
    <FormProvider {...methods}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>NO. MT: {mtData?.number}</Typography>
                <Chip
                  label={mtData?.deliveryNotesCount > 0 ? `${mtData?.deliveryNotesCount} SJ dibuat` : 'SJ belum dibuat'}
                  color={mtData?.deliveryNotesCount > 0 ? 'success' : 'default'}
                  variant='tonal'
                  size='small'
                />
                <Chip
                  label={mtData?.remainingQuantity > 0 ? 'Belum Terpenuhi' : 'Full Supply'}
                  color={mtData?.remainingQuantity > 0 ? 'warning' : 'success'}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(mtData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
              </Typography>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={4}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DocNumberCard warehouseDoc={mrData} docType='MR' />
            </Grid>
            <Grid item xs={12}>
              <InfoCard />
            </Grid>
            {logList?.length > 0 && (
              <Grid item xs={12}>
                <ActivityLogCard logList={logList} />
              </Grid>
            )}
          </Grid>
        </Grid>
        <Grid item xs={12} md={8}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Card>
                <CardHeader
                  title='Barang Diterima'
                  action={
                    <LoadingButton
                      startIcon={<></>}
                      loading={isLoading}
                      loadingPosition='start'
                      variant='contained'
                      disabled={payloadListWatch.length === 0}
                      color={payloadListWatch.length === 0 ? 'secondary' : 'primary'}
                      onClick={handleSubmit(onSubmitHandler)}
                      className='px-8 is-full !ml-0 sm:is-auto'
                    >
                      Perbarui Stok
                    </LoadingButton>
                  }
                />
                <CardContent>
                  <div className='flex flex-col gap-6'>
                    {sjList.length > 0 ? (
                      <>
                        {sjList.map((sj, index) => (
                          <ReceiveTableCard sj={sj} index={index} />
                        ))}
                      </>
                    ) : (
                      <div className='flex flex-col items-center justify-center p-4'>
                        <Typography variant='h5'>Barang sudah diterima</Typography>
                        <Typography className='text-sm text-gray-400'>
                          Semua barang sudah diterima dan ditambahkan ke stok
                        </Typography>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12}>
              <TableCard />
            </Grid>
            <Grid item xs={12}>
              <SjListCard />
            </Grid>
            <Grid item xs={12}>
              <OwnerCard user={mtData?.createdByUser} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default ReceiveSjPage
