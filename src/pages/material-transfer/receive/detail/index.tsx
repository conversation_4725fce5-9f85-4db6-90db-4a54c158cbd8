// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrumbs, Button, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import ActivityLogCard from './components/ActivityLogCard'
import { mtStatusOptions } from '../../config/options'
import { useMt } from '../../context/MtContext'
import { statusChipColor } from '../config/table'
import InfoCard from './components/InfoCard'
import TableCard from '../../component/table'
import { FormProvider, useForm } from 'react-hook-form'
import { MtPayload } from '../../config/types'
import { useEffect } from 'react'
import { MaterialTransferType } from '../../config/enum'

const MtCreatedDetailPage = () => {
  const { mtData, logList, fetchLogList, setSelectedMt, fetchMtList, fetchMtData } = useMt()

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>MT</Typography>
          </Link>
          <Link to='/mt/created' replace>
            <Typography color='var(--mui-palette-text-disabled)'>MT Terbuat</Typography>
          </Link>
          <Typography>Detil MT</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>NO. MT: {mtData?.number}</Typography>
              <Chip
                label={mtStatusOptions.find(option => option.value === mtData?.status)?.label}
                color={statusChipColor[mtData?.status]?.color}
                variant='tonal'
                size='small'
              />
              <Chip
                label={mtData?.remainingQuantity > 0 ? 'Belum Terpenuhi' : 'Full Supply'}
                color={mtData?.remainingQuantity > 0 ? 'warning' : 'success'}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(mtData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button> */}
            </div>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <InfoCard />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={logList} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <TableCard />
          </Grid>
          {(mtData?.approvals?.length ?? 0) > 0 ? (
            <Grid item xs={12}>
              <ApprovalDetailCard approvalList={mtData?.approvals ?? []} />
            </Grid>
          ) : null}
          <Grid item xs={12}>
            <OwnerCard user={mtData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default MtCreatedDetailPage
