import { DepartmentType, ImageType, ItemType, SiteType, UnitType, VendorType } from '@/types/companyTypes'
import { ImagePayload, ListParams, UserIdPayload } from '@/types/payload'
import { ApproverType, UserOutlineType, UserType } from '@/types/userTypes'
import { WarehouseDataType, WarehouseItemType, WarehouseLogType } from '@/types/appTypes'
import { CodeNameType } from '@/types/common'
import { DeliveryNoteStatus, DeliveryNoteType, MaterialTransferApprovalStatus, MaterialTransferStatus } from './enum'

export type MtType = {
  id?: string
  number?: string
  type?: string
  itemsCount?: number
  approvalsCount?: number
  deliveryNotesCount?: number
  note?: string
  createdDocumentsCount?: number
  cancelationNote?: string
  borrowDuration?: number
  returnDueDate?: string
  status?: string
  companyId?: string
  parentCompanyId?: string
  departmentId?: string
  requesterSiteId?: string
  requestedSiteId?: string
  materialRequestId?: string
  receivedQuantity?: number
  remainingQuantity?: number
  returnedQuantity?: number
  isClosed?: boolean
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  items?: MtItemType[]
  approvals?: ApproverType[]
  department?: DepartmentType
  requesterSite?: CodeNameType
  requestedSite?: CodeNameType
  createdByUser?: UserOutlineType
} & WarehouseDataType

export type MtPayload = {
  mtId?: string
  materialRequestId?: string
  type?: string
  requestedSiteId?: string
  items?: MtItemType[]
  approvals?: UserIdPayload[]
  note?: string
  borrowDuration?: number
}

export type MtItemType = {
  id?: number
  itemId?: string
  originalItemId?: string
  systemStock?: number
  mrQuantity?: number
  quantity?: number
  quantityUnit?: string
  largeUnitQuantity?: number
  note?: string
  item?: ItemType
  remainingQuantity?: number
  materialTransferId?: string
  isLargeUnit?: boolean
  smallQuantity?: number
  returnedQuantity?: number
  isActive?: boolean
  receivedQuantity?: number
  receivedQuantityUnit?: string
  receivedLargeUnitQuantity?: number
  receiptNote?: string
} & WarehouseItemType

export type MtApprovalPayload = {
  mtId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: MaterialTransferApprovalStatus
  isRead?: boolean
}

export type MtLogType = {
  id?: number
  type?: string
  status?: string
  changes?: string
  attachmentUrl?: string
  attachmentMimeType?: string
  materialTransferId?: string
  userId?: string
  materialTransferItemId?: number
  createdAt?: string
  user?: UserOutlineType
  materialTransferItem?: MtItemType
} & WarehouseLogType

export type MtParams = {
  isClosed?: boolean
  status?: string
  materialTransferId?: string
  materialRequestId?: string
  requestedSiteId?: string
  requesterSiteId?: string
  requestedSiteIds?: string
  requesterSiteIds?: string
  isHasDeliveryNote?: boolean
  userRole?: string
} & ListParams

export type CancelMtPayload = {
  mtId?: string
  cancelationType?: string
  approvals?: UserIdPayload[]
  cancelationProofUploadId?: string
  cancelationNote?: string
}

export type SjType = {
  id?: string
  number?: string
  itemsCount?: number
  note?: string
  receiptNote?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  receivedAt?: string
  createdBy?: string
  receivedBy?: string
  items?: SjItemType[]
  journalId?: string
  createdByUser?: UserOutlineType
  receivedByUser?: UserOutlineType
}

export type SjItemType = {
  materialTransferItemId?: number
} & MtItemType

export type SjPayload = {
  mtId?: string
  type?: string
  items?: SjItemType[]
  note?: string
}

export type SjReceiptPayloadList = {
  payloads?: SjReceiptPayload[]
}

export type SjReceiptPayload = {
  sjId?: string
  items?: MtItemType[]
  receiptNote?: string
}

export type SjParams = {
  status?: DeliveryNoteStatus
  type?: DeliveryNoteType
} & ListParams

export type SjReceiptType = {
  id?: string
  number?: string
  itemsCount?: number
  note?: string
  receiptNote?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  receivedAt?: string
  createdBy?: string
  receivedBy?: string
  items?: MtItemType[]
  createdByUser?: UserOutlineType
  receivedByUser?: UserOutlineType
}
