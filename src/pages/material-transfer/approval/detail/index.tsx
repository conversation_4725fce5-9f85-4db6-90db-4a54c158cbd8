// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrumbs, Button, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ActivityLogCard from './components/ActivityLogCard'
import { mtStatusOptions } from '../../config/options'
import { useMt } from '../../context/MtContext'
import { statusChipColor } from '../config/table'
import InfoCard from './components/InfoCard'
import { useEffect } from 'react'
import { useReadMtApproval } from '@/api/services/mt/mutation'
import { useAuth } from '@/contexts/AuthContext'
import ApprovalsCard, { statusChipValue } from './components/ApprovalsCard'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import ItemListCard from '../../created/detail/components/ItemListCard'
import { MaterialTransferType, UserRole } from '../../config/enum'
import { MrStatus } from '@/types/mrTypes'
import UnitCard from '@/pages/material-request/create/components/UnitCard'

const MtApprovalDetailPage = () => {
  const { userProfile } = useAuth()
  const { mtData, logList, fetchMtList, mrData } = useMt()

  const { mutate: readMutate } = useReadMtApproval()

  const ownApproval = mtData?.approvals?.find(approval => approval.userId === userProfile?.id)

  useEffect(() => {
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          isRead: true,
          mtId: mtData?.id,
          approvalId: ownApproval.id
        },
        { onSuccess: () => fetchMtList() }
      )
    }
  }, [mtData])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Material Transfer</Typography>
          </Link>
          <Link to='/mt/approval' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Persetujuan Material Transfer</Typography>
          </Link>
          <Typography>Detil Material Transfer</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>NO. MT: {mtData?.number}</Typography>
              {ownApproval && (
                <>
                  {mtData?.status !== MrStatus.CANCELED ? (
                    <Chip
                      label={statusChipValue[ownApproval?.status]?.label}
                      color={statusChipValue[ownApproval?.status]?.color}
                      variant='tonal'
                      size='small'
                    />
                  ) : (
                    <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
                  )}
                </>
              )}
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(mtData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button> */}
            </div>
          </div>
        </div>
      </Grid>
      {mtData?.items ? (
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
      ) : null}
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DocNumberCard warehouseDoc={mrData} docType='MR' />
          </Grid>
          <Grid item xs={12}>
            <InfoCard />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={logList} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {mrData?.unit && (
            <Grid item xs={12}>
              <UnitCard warehouseData={mrData} />
            </Grid>
          )}
          {mtData?.type === MaterialTransferType.IN_SITE ? (
            <Grid item xs={12}>
              <ApprovalsCard role={MaterialTransferType.IN_SITE} />
            </Grid>
          ) : (
            <>
              {(mtData?.approvals?.filter(approval => approval.role === UserRole.REQUESTER)?.length ?? 0) > 0 ? (
                <Grid item xs={12}>
                  <ApprovalsCard role={UserRole.REQUESTER} />
                </Grid>
              ) : null}
              {(mtData?.approvals?.filter(approval => approval.role === UserRole.REQUESTED)?.length ?? 0) > 0 ? (
                <Grid item xs={12}>
                  <ApprovalsCard role={UserRole.REQUESTED} />
                </Grid>
              ) : null}
            </>
          )}
          <Grid item xs={12}>
            <OwnerCard user={mtData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default MtApprovalDetailPage
