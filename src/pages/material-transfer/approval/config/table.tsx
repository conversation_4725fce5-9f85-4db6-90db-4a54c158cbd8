import { StatusChipColorType } from '@/types/appTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { mtStatusOptions } from '../../config/options'
import { MaterialTransferStatus } from '../../config/enum'
import { MtType } from '../../config/types'
import { statusChipValue } from '../detail/components/ApprovalsCard'
import { MrPriority, mrPriorityOptions } from '@/pages/material-request/create/config/enum'
import { MrStatus } from '@/types/mrTypes'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [MaterialTransferStatus.PROCESSED]: { color: 'warning' },
  [MaterialTransferStatus.APPROVED]: { color: 'success' },
  [MaterialTransferStatus.CANCELED]: { color: 'error' },
  [MaterialTransferStatus.REJECTED]: { color: 'error' },
  [MaterialTransferStatus.CANCEL_REQUESTED]: { color: 'error' },
  [MaterialTransferStatus.CLOSED]: { color: 'info' }
}

type MtTypeWithAction = MtType & {
  action?: string
  isRead?: boolean
}

type RowActionType = {
  showDetail: (id: string, isCancelation?: boolean) => void
}

// Column Definitions
const columnHelper = createColumnHelper<MtTypeWithAction>()

export const tableColumns = (rowAction: RowActionType, userId?: string): any[] => [
  columnHelper.accessor('number', {
    header: 'No. MT',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => {
      const docStatus = row.original.status
      const ownApproval = row.original.approvals?.find(approval => approval.userId === userId)
      return docStatus !== MrStatus.CANCELED ? (
        <Chip
          label={statusChipValue[ownApproval?.status]?.label}
          color={statusChipValue[ownApproval?.status]?.color}
          variant='tonal'
          size='small'
        />
      ) : (
        <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
      )
    }
  }),
  columnHelper.accessor('requestedSite', {
    header: 'Permintaan Dari',
    cell: ({ row }) => <Typography>{row.original.requesterSite?.name}</Typography>
  }),
  columnHelper.accessor('requestedSite', {
    header: 'Permintaan Ke',
    cell: ({ row }) => <Typography>{row.original.requestedSite?.name}</Typography>
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => <Typography>{row.original.department?.name}</Typography>
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = mrPriorityOptions.find(option => option.value === String(row.original.priority ?? MrPriority.P4))
      return (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal Dibuat',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('createdByUser', {
    header: 'Dibuat Oleh',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.createdByUser?.fullName}</Typography>
        <Typography variant='caption'>{row.original.createdByUser?.title}</Typography>
      </div>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
