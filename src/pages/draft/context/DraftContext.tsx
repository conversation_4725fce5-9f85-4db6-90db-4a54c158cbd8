import { defaultListData } from '@/api/queryClient'
import { useCreateDraft, useDeleteDraft, useUpdateDraft } from '@/api/services/draft/mutation'
import DraftQueryMethods from '@/api/services/draft/query'
import DraftServices, { DRAFT_QUERY_KEY, DRAFT_QUERY_LIST_KEY } from '@/api/services/draft/service'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ApiResponse, ListResponse } from '@/types/api'
import { DraftParams, DraftPayload, DraftScope, DraftType } from '@/types/draftsTypes'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  UseMutateFunction,
  useQuery
} from '@tanstack/react-query'
import React from 'react'
import { toast } from 'react-toastify'
import { useLocation, useSearchParams } from 'react-router-dom'
import { CreatePrInput } from '@/pages/purchase-requisition/create/config/schema'
import { useAuth } from '@/contexts/AuthContext'
import { PoPayload } from '@/pages/purchase-order/config/types'
import { MgOutPayload } from '@/types/mgTypes'
import { SrPayload } from '@/types/srTypes'
import { RmaPayload } from '@/pages/rma/config/types'
import { MtPayload } from '@/pages/material-transfer/config/types'
import { CreateWorkOrderDtoType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { WoPartSwapDtoType } from '@/types/partSwapTypes'
import { ServiceRequisitionPayload } from '@/types/serviceRequisitionsTypes'
import { CreateReconciliationPayload } from '@/pages/cash-bank/reconsiliation/config/types'

type DraftContextProps = {
  draftParams: DraftParams
  setPartialDraftParams: (fieldName: keyof DraftParams, value: any) => void
  setDraftParams: React.Dispatch<React.SetStateAction<DraftParams>>
  deleteDraft: UseMutateFunction<ApiResponse<any>, Error, string, void>
  draftListResponse: ListResponse<DraftType>
  handleDeleteDraft: (id: string) => void
  refetchDraft: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<DraftType>, unknown>>
  refetchDraftData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<DraftType, unknown>>
  createDraft: UseMutateFunction<ApiResponse<DraftType>, Error, DraftPayload, void>
  updateDraft: UseMutateFunction<
    ApiResponse<DraftType>,
    Error,
    {
      draftId: string
      payload: string
      siteId: string
    },
    void
  >
  loadingDraft: boolean
  pageName: string
  redirect: (row: DraftType) => Promise<string>
  isMobile: boolean
  draftData: DraftType
}

const DraftContext = React.createContext<DraftContextProps>({} as DraftContextProps)

export const useDraft = () => {
  const context = React.useContext(DraftContext)
  if (context) {
    return context
  }
  throw new Error('useDraft must be used within a DraftContext')
}

export const DraftContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { pathname } = useLocation()
  const [searchParams] = useSearchParams()
  const { setConfirmState } = useMenu()
  const { ownSiteList } = useAuth()
  const [draftParams, setPartialDraftParams, setDraftParams] = usePartialState<DraftParams>({
    page: 1,
    limit: 10
  })

  const isMateriaRequest = pathname.includes('mr')
  const isPurchaseRequisition = pathname.includes('pr')
  const isPurchaseOrder = pathname.includes('po')
  const isPurchaseInvoice = pathname.includes('purchase-invoice')
  const isSalesInvoice = pathname.includes('sales/invoice')
  const isOutgoingMaterial = pathname.includes('mg/out')
  const isRma = pathname.includes('rma')
  const isStockReturn = pathname.includes('sr')
  const isStockMove = pathname.includes('mg/sm')
  const isMaterialTransfer = pathname.includes('mt')
  const isMaterialBorrow = pathname.includes('mb')
  const isPayment = pathname.includes('/cash-bank/payments')
  const isCashReceipt = pathname.includes('/cash-bank/receipt')
  const isGeneralLedger = pathname.includes('/accounting/general-ledger')
  const isFieldReport = pathname.includes('/fr/draft')
  const isWorkOrder = pathname.includes('/wo/draft')
  const isWorkProcess = pathname.includes('/wp/draft')
  const isPartSwap = pathname.includes('part-swap')
  const isServiceRequest = pathname.includes('service-request')
  const isBankReconciliation = pathname.includes('reconciliation')
  const isAsset = pathname.includes('/assets/unit')
  const isEndPeriod = pathname.includes('end-periods')

  const scopeNameMap: Record<DraftScope, string> = {
    [DraftScope['MATERIAL-REQUEST']]: 'Material Request',
    [DraftScope['PURCHASE-REQUISITION']]: 'Purchase Requisition',
    [DraftScope['PURCHASE-ORDER']]: 'Purchase Order',
    [DraftScope['PURCHASE-INVOICE']]: 'Faktur Pembelian',
    [DraftScope['OUTGOING-MATERIAL']]: 'Barang Keluar',
    [DraftScope.RMA]: 'RMA',
    [DraftScope['STOCK-RETURN']]: 'Pengembalian Stok',
    [DraftScope['STOCK-MOVEMENT']]: 'Pindah Stok',
    [DraftScope['MATERIAL-TRANSFER']]: 'Material Transfer',
    [DraftScope['MATERIAL-BORROW']]: 'Material Borrow',
    [DraftScope.PAYMENT]: 'Pembayaran',
    [DraftScope['CASH-RECEIPT']]: 'Penerimaan',
    [DraftScope['SALES-INVOICE']]: 'Faktur Penjualan',
    [DraftScope['GENERAL-LEDGER']]: 'Jurnal Umum',
    [DraftScope['FIELD-REPORT']]: 'Field Report',
    [DraftScope['WORK-ORDER']]: 'Work Order',
    [DraftScope['WORK-PROCESS']]: 'Work Process',
    [DraftScope['PART-SWAP']]: 'Part Swap',
    [DraftScope['SERVICE-REQUEST']]: 'Service Request',
    [DraftScope['BANK-RECONCILIATION']]: 'Bank Reconciliation',
    [DraftScope.ASSET]: 'Asset',
    [DraftScope['END-PERIOD']]: 'End Period'
  }

  const getScopeFromPathname = (): string | undefined => {
    if (isMateriaRequest) {
      return scopeNameMap[DraftScope['MATERIAL-REQUEST']]
    } else if (isPurchaseRequisition) {
      return scopeNameMap[DraftScope['PURCHASE-REQUISITION']]
    } else if (isPurchaseOrder) {
      return scopeNameMap[DraftScope['PURCHASE-ORDER']]
    } else if (isPurchaseInvoice) {
      return scopeNameMap[DraftScope['PURCHASE-INVOICE']]
    } else if (isSalesInvoice) {
      return scopeNameMap[DraftScope['SALES-INVOICE']]
    } else if (isOutgoingMaterial) {
      return scopeNameMap[DraftScope['OUTGOING-MATERIAL']]
    } else if (isRma) {
      return scopeNameMap[DraftScope.RMA]
    } else if (isStockReturn) {
      return scopeNameMap[DraftScope['STOCK-RETURN']]
    } else if (isStockMove) {
      return scopeNameMap[DraftScope['STOCK-MOVEMENT']]
    } else if (isMaterialTransfer) {
      return scopeNameMap[DraftScope['MATERIAL-TRANSFER']]
    } else if (isMaterialBorrow) {
      return scopeNameMap[DraftScope['MATERIAL-BORROW']]
    } else if (isPayment) {
      return scopeNameMap[DraftScope.PAYMENT]
    } else if (isCashReceipt) {
      return scopeNameMap[DraftScope['CASH-RECEIPT']]
    } else if (isGeneralLedger) {
      return scopeNameMap[DraftScope['GENERAL-LEDGER']]
    } else if (isFieldReport) {
      return scopeNameMap[DraftScope['FIELD-REPORT']]
    } else if (isWorkOrder) {
      return scopeNameMap[DraftScope['WORK-ORDER']]
    } else if (isWorkProcess) {
      return scopeNameMap[DraftScope['WORK-PROCESS']]
    } else if (isPartSwap) {
      return scopeNameMap[DraftScope['PART-SWAP']]
    } else if (isServiceRequest) {
      return scopeNameMap[DraftScope['SERVICE-REQUEST']]
    } else if (isBankReconciliation) {
      return scopeNameMap[DraftScope['BANK-RECONCILIATION']]
    } else if (isAsset) {
      return scopeNameMap[DraftScope.ASSET]
    } else if (isEndPeriod) {
      return scopeNameMap[DraftScope['END-PERIOD']]
    }
    return undefined
  }

  const redirectCreateDraft = async (row: DraftType): Promise<string> => {
    if (isMateriaRequest) {
      return '/mr/create'
    } else if (isPurchaseRequisition) {
      const { data } = await DraftServices.getDraftById(row.id)
      const payload: CreatePrInput = JSON.parse(data.payload)
      return `/mr-in/${payload.materialRequestId}/create-pr`
    } else if (isPurchaseOrder) {
      const { data } = await DraftServices.getDraftById(row.id)
      const payload: PoPayload = JSON.parse(data.payload)
      return `/po/pr-list/${payload.purchaseRequisitionIds?.[0]}/po/create?items=${payload.items.map(item => item.id).join(',')}`
    } else if (isPurchaseInvoice) {
      return `/purchase-invoice/create`
    } else if (isSalesInvoice) {
      return `/sales/invoice/create`
    } else if (isOutgoingMaterial) {
      const { data } = await DraftServices.getDraftById(row.id)
      const payload: MgOutPayload = JSON.parse(data.payload)
      return `/mr/my-list/${payload.materialRequestId}/create-mg-out`
    } else if (isRma) {
      const { data } = await DraftServices.getDraftById(row.id)
      const payload: RmaPayload = JSON.parse(data.payload)
      return `/rma/request/${payload.purchaseOrderId}/create-rma`
    } else if (isStockReturn) {
      const { data } = await DraftServices.getDraftById(row.id)
      const payload: SrPayload = JSON.parse(data.payload)
      return `/sr/request/${payload.materialRequestId}/create-sr?hasBeenOut=${payload.hasBeenOut}`
    } else if (isStockMove) {
      return ''
    } else if (isMaterialTransfer) {
      const { data } = await DraftServices.getDraftById(row.id)
      const payload: MtPayload = JSON.parse(data.payload)
      return `/mr-in/${payload.materialRequestId}/create-mt`
    } else if (isMaterialBorrow) {
      return ''
    } else if (isPayment) {
      return '/cash-bank/payments/create'
    } else if (isCashReceipt) {
      return '/cash-bank/receipt/create'
    } else if (isGeneralLedger) {
      return '/accounting/general-ledger/create'
    } else if (isFieldReport) {
      return '/fr/new-fr'
    } else if (isWorkOrder) {
      const { data } = await DraftServices.getDraftById(row.id)
      const payload: CreateWorkOrderDtoType = JSON.parse(data.payload)
      return `/wo/list/${payload.fieldReportId}/create-wo`
    } else if (isWorkProcess) {
      return '/wp/create'
    } else if (isPartSwap) {
      return '/part-swap/create'
    } else if (isServiceRequest) {
      return '/service-request/create'
    } else if (isBankReconciliation) {
      return '/reconciliation/create'
    } else if (isAsset) {
      return '/company-data/assets/unit/-/new'
    } else if (isEndPeriod) {
      return '/accounting/end-periods/list'
    }
    return ''
  }

  const { mutate: deleteDraft } = useDeleteDraft()
  const { mutate: createDraft, isLoading: createDraftLoading } = useCreateDraft()
  const { mutate: updateDraft, isLoading: updateDraftLoading } = useUpdateDraft()

  const loadingDraft = createDraftLoading || updateDraftLoading

  const { data: draftData, refetch: refetchDraftData } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  const { data: draftListResponse, refetch: refetchDraft } = useQuery({
    enabled: ownSiteList?.length > 0,
    queryKey: [DRAFT_QUERY_LIST_KEY, JSON.stringify(draftParams), pathname, JSON.stringify(ownSiteList)],
    queryFn: () => {
      let scope: DraftScope
      if (isMateriaRequest) {
        scope = DraftScope['MATERIAL-REQUEST']
      } else if (isPurchaseRequisition) {
        scope = DraftScope['PURCHASE-REQUISITION']
      } else if (isPurchaseOrder) {
        scope = DraftScope['PURCHASE-ORDER']
      } else if (isPurchaseInvoice) {
        scope = DraftScope['PURCHASE-INVOICE']
      } else if (isSalesInvoice) {
        scope = DraftScope['SALES-INVOICE']
      } else if (isOutgoingMaterial) {
        scope = DraftScope['OUTGOING-MATERIAL']
      } else if (isRma) {
        scope = DraftScope.RMA
      } else if (isStockReturn) {
        scope = DraftScope['STOCK-RETURN']
      } else if (isStockMove) {
        scope = DraftScope['STOCK-MOVEMENT']
      } else if (isMaterialTransfer) {
        scope = DraftScope['MATERIAL-TRANSFER']
      } else if (isMaterialBorrow) {
        scope = DraftScope['MATERIAL-BORROW']
      } else if (isPayment) {
        scope = DraftScope.PAYMENT
      } else if (isCashReceipt) {
        scope = DraftScope['CASH-RECEIPT']
      } else if (isGeneralLedger) {
        scope = DraftScope['GENERAL-LEDGER']
      } else if (isFieldReport) {
        scope = DraftScope['FIELD-REPORT']
      } else if (isWorkOrder) {
        scope = DraftScope['WORK-ORDER']
      } else if (isWorkProcess) {
        scope = DraftScope['WORK-PROCESS']
      } else if (isPartSwap) {
        scope = DraftScope['PART-SWAP']
      } else if (isServiceRequest) {
        scope = DraftScope['SERVICE-REQUEST']
      } else if (isBankReconciliation) {
        scope = DraftScope['BANK-RECONCILIATION']
      } else if (isAsset) {
        scope = DraftScope.ASSET
      } else if (isEndPeriod) {
        scope = DraftScope['END-PERIOD']
      }
      // const payload = { ...draftParams, scope, siteIds: ownSiteList.map(site => site.id).join(',') }
      const payload = { ...draftParams, scope }
      return DraftQueryMethods.getDrafts(payload)
    },
    cacheTime: 0,
    placeholderData: defaultListData as ListResponse<DraftType>
  })

  const handleDeleteDraft = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Draft',
      content: 'Anda yakin ingin menghapus data draft ini? Action ini tidak dapat diubah lagi',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        deleteDraft(id, {
          onSuccess: () => {
            toast.success('Draft Berhasil dihapus!')
            refetchDraft()
          }
        })
      }
    })
  }

  const value = {
    isMobile,
    draftParams,
    setPartialDraftParams,
    setDraftParams,
    draftListResponse,
    draftData,
    refetchDraft,
    refetchDraftData,
    deleteDraft,
    handleDeleteDraft,
    pageName: getScopeFromPathname(),
    redirect: redirectCreateDraft,
    createDraft,
    updateDraft,
    loadingDraft
  }
  return <DraftContext.Provider value={value}>{children}</DraftContext.Provider>
}
