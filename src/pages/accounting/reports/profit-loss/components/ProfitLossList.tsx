import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, Divider, Typography } from '@mui/material'
import { useProfitLoss } from '../../context/ProfitLossContext'
import { PDFDownloadLink } from '@react-pdf/renderer'
import { profitAndLossReportOptions } from '../../finance/components/dialogs/utils'
import { format, toDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { classNames, toCurrency } from '@/utils/helper'
import { useQuery } from '@tanstack/react-query'
import ReportQueryMethods, { PROFIT_LOSS_QUERY_KEY } from '@/api/services/reports/query'
import { JournalProfitLoss, JournalReportParams, JournalReportType } from '@/types/reportTypes'
import { useAuth } from '@/contexts/AuthContext'

const ProfitLossList = () => {
  const { userProfile } = useAuth()
  const { site, filter, allSearchParams, setProfitLoss } = useProfitLoss()
  const { startDate, endDate, reportType } = filter

  const {
    data: profitLossReports,
    isLoading,
    isFetching
  } = useQuery({
    queryKey: [PROFIT_LOSS_QUERY_KEY, JSON.stringify(allSearchParams)],
    queryFn: async () => {
      const params = {
        startDate: toDate(startDate).toISOString(),
        endDate: toDate(endDate).toISOString(),
        includeHeaderBalances: allSearchParams?.showParentBalance === 'true',
        includeZeroAccounts: allSearchParams?.showZeroBalance === 'true',
        companyId: userProfile?.company?.id,
        type: JournalReportType.STANDARD
      } as JournalReportParams
      const res = await ReportQueryMethods.getJournalProfitLossReports(params)
      setProfitLoss(res)
      return res
    }
  })

  type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total'
  type PLRow = {
    variant: RowVariant
    label: string
    amount?: number | null
    indent?: number
    color?: 'default' | 'error'
  }

  const getPLRows = (data: JournalProfitLoss | undefined): PLRow[] => {
    const allRows: PLRow[] = []
    if (!data) return allRows

    data.sections.forEach(section => {
      allRows.push({ variant: 'section', label: section.title })
      section.subSections.forEach(subSection => {
        allRows.push({ variant: 'group', label: subSection.headerName, indent: 1 })
        subSection.accounts.forEach(account => {
          const label = account.isContra ? `(-) ${account.name}` : account.name
          allRows.push({
            variant: 'item',
            label: label,
            amount: account.amount,
            indent: 2
          })
        })
        if (subSection.accounts.length > 0) {
          allRows.push({ variant: 'subtotal', label: `Jumlah ${subSection.headerName}`, amount: subSection.subTotal })
        }
      })
      allRows.push({ variant: 'total', label: `Jumlah ${section.title}`, amount: section.subTotal })
    })

    return allRows
  }

  const rows: PLRow[] = getPLRows(profitLossReports)

  const renderRow = (row: PLRow, idx: number) => {
    const amountText = row.amount !== null && row.amount !== undefined ? toCurrency(row.amount) : '-'
    const ml = row.indent ? row.indent * 16 : 0

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-2 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <div key={idx} className='col-span-2'>
            <Typography className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
          </div>
        )
      case 'item':
        return (
          <>
            <Typography key={`l-${idx}`} style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography key={`r-${idx}`} align='right'>
              {amountText}
            </Typography>
          </>
        )
      case 'subtotal':
        return (
          <>
            <div key={`d-${idx}`} className='col-start-2'>
              <Divider variant='inset' />
            </div>
            <Typography key={`ls-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            <Typography key={`rs-${idx}`} align='right' className='font-semibold'>
              {amountText}
            </Typography>
          </>
        )
      case 'total':
        return (
          <>
            <div key={`d2-${idx}`} className='col-start-2'>
              <Divider variant='inset' />
            </div>
            <Typography key={`lt-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            <Typography
              key={`rt-${idx}`}
              align='right'
              className={`font-semibold ${row.amount && row.amount < 0 ? 'text-error' : ''}`}
            >
              {amountText}
            </Typography>
          </>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Laba Rugi ({profitAndLossReportOptions?.find(option => option.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {`${format(profitLossReports?.params?.startDate ?? allSearchParams?.startDate, 'dd MMM yyyy', { locale: id })} - ${format(profitLossReports?.params?.endDate ?? allSearchParams?.endDate, 'dd MMM yyyy', { locale: id })}`}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-20'>
          <CardHeader
            action={
              <Typography align='right' className='font-normal text-textPrimary text-sm'>
                {`${format(allSearchParams?.startDate, 'dd/MM/yyyy', { locale: id })} - ${format(allSearchParams?.endDate, 'dd/MM/yyyy', { locale: id })}`}
              </Typography>
            }
            title={<Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>}
            className='bg-[#DBF7E8]'
          />
          <CardContent className='grid grid-cols-2 gap-2 p-4 mt-4'>
            {isLoading || isFetching ? (
              <div className='col-span-2 flex justify-center'>
                <Typography>Loading...</Typography>
              </div>
            ) : (
              rows.map((row, idx) => renderRow(row, idx))
            )}
          </CardContent>
          <CardActions className='p-0'>
            <div
              className={classNames(
                'flex justify-between w-full h-full p-4',
                profitLossReports?.summary?.netIncome > 0 ? 'bg-[#DBF7E8]' : 'bg-red-100'
              )}
            >
              <Typography variant='h6'>Laba/Rugi Bersih (After Tax)</Typography>
              <Typography variant='h6' color={profitLossReports?.summary?.netIncome > 0 ? 'primary' : 'error'}>
                {toCurrency(profitLossReports?.summary?.netIncome)}
              </Typography>
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default ProfitLossList
