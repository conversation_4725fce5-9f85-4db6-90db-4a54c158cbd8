import { defaultListData } from '@/api/queryClient'
import AccountsQueryMethods, {
  END_PERIOD_LIST_QUERY_KEY,
  GENERAL_LEDGER_LIST_QUERY_KEY,
  GENERAL_LEDGER_QUERY_KEY
} from '@/api/services/account/query'
import usePartialState from '@/core/hooks/usePartialState'
import { EndPeriodLogType, EndPeriodType, GeneralLedgerType } from '@/types/accountTypes'
import { QueryFn } from '@/types/alias'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import { createContext, useContext, useEffect, useState } from 'react'
import AddEndPeriodsDialog from '../components/add-end-periods-dialog'
import { useParams, useSearchParams } from 'react-router-dom'
import { decryptorId } from '../config/utils'

type EndPeriodContextProps = {
  endPeriodListResponse: ListResponse<EndPeriodType>
  fetchEndPeriodList: QueryFn<ListResponse<EndPeriodType>>
  endPeriodData: EndPeriodType
  endPeriodLogs: ListResponse<EndPeriodLogType>
  fetchEndPeriodData: QueryFn<EndPeriodType>
  selectedPeriod: string
  setSelectedPeriod: React.Dispatch<React.SetStateAction<string>>
  endperiodParams: ListParams
  setEndperiodParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialEndperiodParams: (fieldName: keyof ListParams, value: any) => void
  setAddEndPeriodDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
  journalData: GeneralLedgerType
  fetchJournalData: QueryFn<GeneralLedgerType>
  fetchEndPeriodLogs: QueryFn<ListResponse<EndPeriodLogType>>
  generalLedgerList: GeneralLedgerType[]
  fetchGeneralLedgerList: QueryFn<ListResponse<GeneralLedgerType>>
}

const EndPeriodContext = createContext<EndPeriodContextProps>({} as EndPeriodContextProps)

export const useEndPeriod = () => {
  const context = useContext(EndPeriodContext)
  if (context === undefined) {
    throw new Error('useEndPeriod must be used within EndPeriodContextProvider')
  }
  return context
}

export const EndPeriodContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { endPeriodId } = useParams()
  const [searchParams] = useSearchParams()
  const [addEndPeriodDialogOpen, setAddEndPeriodDialogOpen] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<string>()
  const [endperiodParams, setPartialEndperiodParams, setEndperiodParams] = usePartialState<ListParams>({
    limit: 10,
    page: 1
  })

  const { data: endPeriodListResponse, refetch: fetchEndPeriodList } = useQuery({
    queryKey: [END_PERIOD_LIST_QUERY_KEY, JSON.stringify(endperiodParams)],
    queryFn: () => AccountsQueryMethods.getEndPeriodList(endperiodParams),
    placeholderData: defaultListData as ListResponse<EndPeriodType>
  })

  const { data: endPeriodData, refetch: fetchEndPeriodData } = useQuery({
    enabled: !!selectedPeriod,
    queryKey: [END_PERIOD_LIST_QUERY_KEY, selectedPeriod],
    queryFn: () => AccountsQueryMethods.getEndPeriod(decryptorId(selectedPeriod) as string)
  })

  const { data: endPeriodLogs, refetch: fetchEndPeriodLogs } = useQuery({
    enabled: !!selectedPeriod,
    queryKey: [END_PERIOD_LIST_QUERY_KEY, 'LOGS', selectedPeriod],
    queryFn: () => AccountsQueryMethods.getEndPeriodLogs(decryptorId(selectedPeriod) as string)
  })

  const {
    data: { items: generalLedgerList },
    refetch: fetchGeneralLedgerList
  } = useQuery({
    enabled: !!selectedPeriod,
    queryKey: [GENERAL_LEDGER_LIST_QUERY_KEY, selectedPeriod],
    queryFn: () =>
      AccountsQueryMethods.getGeneralLedgerList({
        limit: Number.MAX_SAFE_INTEGER,
        isEndPeriodEntry: true,
        periodId: decryptorId(selectedPeriod) as number
      }),
    placeholderData: defaultListData as ListResponse<GeneralLedgerType>
  })

  const { data: journalData, refetch: fetchJournalData } = useQuery({
    // TODO: uncomment this after real data is available
    // enabled: !!endPeriodData?.journalId,
    queryKey: [GENERAL_LEDGER_QUERY_KEY, JSON.stringify(endPeriodData), endPeriodData?.journalId],
    queryFn: () =>
      // TODO: Remove this after real data is available
      AccountsQueryMethods.getGeneralLedger(endPeriodData?.journalId ?? '0198e416-5a78-7cc4-872b-0add2b405517')
  })

  useEffect(() => {
    if (!!endPeriodId) {
      setSelectedPeriod(endPeriodId)
    }
  }, [endPeriodId])

  useEffect(() => {
    if (searchParams.get('draft')) {
      setAddEndPeriodDialogOpen(true)
    }
  }, [searchParams])

  const value = {
    endPeriodListResponse,
    fetchEndPeriodList,
    endPeriodData,
    fetchEndPeriodData,
    selectedPeriod,
    setSelectedPeriod,
    endperiodParams,
    setEndperiodParams,
    setPartialEndperiodParams,
    setAddEndPeriodDialogOpen,
    endPeriodLogs,
    journalData,
    fetchEndPeriodLogs,
    fetchJournalData,
    generalLedgerList,
    fetchGeneralLedgerList
  }

  return (
    <EndPeriodContext.Provider value={value}>
      {addEndPeriodDialogOpen && (
        <AddEndPeriodsDialog open={addEndPeriodDialogOpen} setOpen={setAddEndPeriodDialogOpen} />
      )}
      {children}
    </EndPeriodContext.Provider>
  )
}
