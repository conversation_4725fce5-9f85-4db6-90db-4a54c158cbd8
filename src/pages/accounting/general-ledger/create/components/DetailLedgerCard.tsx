import {
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  InputLabel,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { GeneralLedgerPayload } from '@/types/payload'
import { toDate } from 'date-fns'
import { useAuth } from '@/contexts/AuthContext'
import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY, PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { UnitType } from '@/types/companyTypes'
import { ProjectLabelType, ProjectType } from '@/types/projectTypes'
import { useEffect, useState } from 'react'
import UnitAutocomplete from '@/components/UnitAutocomplete'
import { ThousandField } from '@/components/numeric/CurrencyField'

const DetailLedgerCard = () => {
  const { control, setValue, reset, getValues } = useFormContext<GeneralLedgerPayload>()
  const { groupedSiteList, departmentList, currenciesList } = useAuth()
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(null)

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  const {
    data: { items: projectLabels }
  } = useQuery({
    queryKey: [PROJECT_LABEL_LIST_QUERY_KEY, siteIdWatch],
    enabled: !!siteIdWatch,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectLabelList({ limit: 100, siteId: siteIdWatch })
    },
    placeholderData: defaultListData as ListResponse<ProjectLabelType>
  })

  const { data: projectListResponse } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  useEffect(() => {
    if (control._getWatch('unit')) {
      setSelectedUnit(control._getWatch('unit') as UnitType)
    } else {
      setSelectedUnit(null)
    }
  }, [control._getWatch('unit')])

  useEffect(() => {
    if (currenciesList?.length > 0) {
      reset({
        ...getValues(),
        currencyId: currenciesList?.find(currency => currency.isDefault)?.id,
        exchangeRate: 1
      })
    }
  }, [currenciesList])

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Pencatatan</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
          {/* <Controller
            control={control}
            name='type'
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <FormControl>
                <InputLabel error={!!error} id='transaction-type'>
                  Tipe Transaksi
                </InputLabel>
                <Select
                  key={value}
                  error={!!error}
                  disabled
                  onChange={onChange}
                  value={value}
                  label='Tipe Transaksi'
                  id='transaction-type'
                  labelId='transaction-type'
                  variant='outlined'
                >
                  {generalLedgerOptions.map(opt => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </Select>
                {!!error && <FormHelperText error>Wajib diisi</FormHelperText>}
              </FormControl>
            )}
          /> */}
          <Controller
            control={control}
            name='transactionDate'
            rules={{ required: true }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <div className='flex flex-col gap-2'>
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(date.toISOString())}
                  dateFormat='dd MMM yyyy'
                  // maxDate={new Date()}
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Transaksi'
                      error={!!error}
                      placeholder='Pilih Tanggal'
                      className='flex-1'
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              </div>
            )}
          />
          <Controller
            control={control}
            name='projectId'
            rules={{ required: true }}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl error={!!error} fullWidth>
                <InputLabel id='site-select'>Proyek</InputLabel>
                <Select
                  value={value}
                  onChange={onChange}
                  labelId='site-select'
                  id='site-select'
                  label='Proyek'
                  placeholder='Pilih Proyek'
                  error={!!error}
                >
                  {projectListResponse?.items?.map(project => (
                    <MenuItem key={project.id} value={project.id}>
                      {project.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />

          <Controller
            control={control}
            name='currencyId'
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl fullWidth error={!!error}>
                <InputLabel id='role-select'>Mata Uang (Opsional)</InputLabel>
                <Select
                  key={value}
                  fullWidth
                  id='select-currencyId'
                  value={value}
                  onChange={e => {
                    onChange((e.target as HTMLInputElement).value)
                  }}
                  label='Mata Uang (Opsional)'
                  labelId='select-currencyId'
                  inputProps={{ placeholder: 'Pilih Kode Mata Uang' }}
                >
                  {currenciesList?.map(currency => (
                    <MenuItem key={currency.id} value={currency.id}>
                      {currency.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='exchangeRate'
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <TextField
                fullWidth
                label='Nilai Tukar (Opsional)'
                placeholder='Nilai Tukar'
                value={value}
                onChange={e => onChange(e.target.value)}
                error={!!error}
                InputProps={{ inputComponent: ThousandField as any }}
                InputLabelProps={{ shrink: !!value }}
              />
            )}
          />
          {/* <Controller
            name='siteId'
            control={control}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl fullWidth error={!!error}>
                <InputLabel id='role-select'>Lokasi (Opsional)</InputLabel>
                <Select
                  key={value}
                  fullWidth
                  id='select-siteId'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                  label='Lokasi (Opsional)'
                  size='medium'
                  labelId='siteId-select'
                  inputProps={{ placeholder: 'Pilih Lokasi' }}
                  defaultValue=''
                >
                  {groupedSiteList.map(group => {
                    let children = []
                    children.push(
                      <ListSubheader
                        className='bg-green-50 text-primary font-semibold'
                        key={group.projectId ?? 'no_project'}
                      >
                        {group.project?.name || 'Tanpa Proyek'}
                      </ListSubheader>
                    )
                    group.sites.forEach(site => {
                      children.push(
                        <MenuItem key={site.id} value={site.id}>
                          <ListItemText primary={site.name} />
                        </MenuItem>
                      )
                    })
                    return children
                  })}
                </Select>
                {error && <FormHelperText>{error.message}</FormHelperText>}
              </FormControl>
            )}
          />

          <Controller
            name='departmentId'
            control={control}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl fullWidth error={!!error}>
                <InputLabel id='role-select'>Departemen (Opsional)</InputLabel>
                <Select
                  key={value}
                  fullWidth
                  id='select-departmentId'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                  label='Departemen (Opsional)'
                  size='medium'
                  labelId='departmentId-select'
                  inputProps={{ placeholder: 'Pilih Departemen' }}
                  defaultValue=''
                >
                  {departmentList?.map(department => (
                    <MenuItem key={department.id} value={department.id}>
                      {department.name}
                    </MenuItem>
                  ))}
                </Select>
                {error && <FormHelperText>{error.message}</FormHelperText>}
              </FormControl>
            )}
          />

          <Controller
            control={control}
            name='projectLabelId'
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl fullWidth error={!!error}>
                <InputLabel id='select-projectLabelId'>Label Proyek (Opsional)</InputLabel>
                <Select
                  key={value}
                  fullWidth
                  id='select-projectLabelId'
                  value={value}
                  disabled={!siteIdWatch}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                  label='Label Proyek (Opsional)'
                  labelId='select-projectLabelId'
                  inputProps={{ placeholder: 'Pilih Label' }}
                >
                  {projectLabels?.map(label => (
                    <MenuItem key={label.id} value={label.id}>
                      {label.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='unitId'
            rules={{
              validate: value => {
                return true
              }
            }}
            render={({ fieldState: { error } }) => (
              <UnitAutocomplete
                value={selectedUnit}
                label='Kode Unit (Opsional)'
                onChange={unit => {
                  if (unit) {
                    setValue('unitId', unit.id)
                    setValue('unit', unit)
                    setSelectedUnit(unit)
                  } else {
                    setValue('unitId', undefined)
                    setValue('unit', null)
                    setSelectedUnit(null)
                  }
                }}
                error={!!error}
                helperText={error?.message}
              />
            )}
          />
          <TextField
            fullWidth
            label='Merk Unit'
            variant='outlined'
            value={selectedUnit?.brandName ?? ''}
            disabled
            InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
          />
          <TextField
            fullWidth
            label='Tipe Unit'
            variant='outlined'
            value={selectedUnit?.type ?? ''}
            disabled
            InputLabelProps={{ shrink: !!selectedUnit?.type }}
          /> */}
          {/* <TextField
            fullWidth
            label='Nomor Lambung'
            variant='outlined'
            value={selectedUnit?.hullNumber ?? ''}
            disabled
            InputLabelProps={{ shrink: !!selectedUnit?.hullNumber }}
          /> */}
          <Controller
            control={control}
            name='description'
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <div className='md:col-span-4'>
                <TextField
                  fullWidth
                  multiline
                  rows={2}
                  label='Keterangan'
                  error={!!error}
                  onChange={onChange}
                  value={value}
                />
              </div>
            )}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default DetailLedgerCard
