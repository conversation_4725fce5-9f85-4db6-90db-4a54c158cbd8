import {
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { getGeneralLedgerType } from '../../config/utils'
import { formatDate } from 'date-fns'
import { useGeneralLedger } from '../../context/GeneralLedgerContext'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { JournalRefDocType } from '@/pages/cash-bank/reconsiliation/config/types'
import { thousandSeparator } from '@/utils/helper'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { GeneralLedgerPayload } from '@/types/payload'
import { useAuth } from '@/contexts/AuthContext'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectLabelType } from '@/types/projectTypes'
import UnitAutocomplete from '@/components/UnitAutocomplete'
import { useState } from 'react'
import { UnitType } from '@/types/companyTypes'

const DetailLedgerCard = () => {
  const { generalLedgerData, isEditable } = useGeneralLedger()
  const { groupedSiteList, departmentList } = useAuth()
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(null)

  const { control, setValue } = useFormContext<GeneralLedgerPayload>()

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  const {
    data: { items: projectLabels }
  } = useQuery({
    queryKey: [PROJECT_LABEL_LIST_QUERY_KEY, siteIdWatch],
    enabled: !!siteIdWatch,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectLabelList({ limit: 100, siteId: siteIdWatch })
    },
    placeholderData: defaultListData as ListResponse<ProjectLabelType>
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Pencatatan</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='flex flex-col gap-2'>
            <small>Tipe Transaksi</small>
            <Typography>{getGeneralLedgerType(generalLedgerData?.type)}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Tanggal Transaksi</small>
            <Typography>
              {generalLedgerData?.transactionDate
                ? formatDate(generalLedgerData.transactionDate, 'dd MMM yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Mata Uang</small>
            <Typography>{!!generalLedgerData?.currencyId ? generalLedgerData?.currency?.name : '-'}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Nilai Tukar</small>
            <Typography>
              {!!generalLedgerData?.exchangeRate
                ? thousandSeparator(generalLedgerData?.exchangeRate, true, null, 2)
                : '-'}
            </Typography>
          </div>

          {/* <div className='flex flex-col gap-2'> */}
          {/* <small>Lokasi</small> */}
          {/* {isEditable ? (
              <Controller
                name='siteId'
                control={control}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl size='small' fullWidth error={!!error}>
                    <Select
                      key={value}
                      fullWidth
                      id='select-siteId'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      // label='Lokasi (Opsional)'
                      size='small'
                      labelId='siteId-select'
                      inputProps={{ placeholder: 'Pilih Lokasi' }}
                      defaultValue=''
                    >
                      {groupedSiteList.map(group => {
                        let children = []
                        children.push(
                          <ListSubheader
                            className='bg-green-50 text-primary font-semibold'
                            key={group.projectId ?? 'no_project'}
                          >
                            {group.project?.name || 'Tanpa Proyek'}
                          </ListSubheader>
                        )
                        group.sites.forEach(site => {
                          children.push(
                            <MenuItem key={site.id} value={site.id}>
                              <ListItemText primary={site.name} />
                            </MenuItem>
                          )
                        })
                        return children
                      })}
                    </Select>
                    {error && <FormHelperText>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            ) : (
              <Typography>{!!generalLedgerData?.site ? generalLedgerData.site?.name : '-'}</Typography>
            )} */}
          {/* </div> */}
          {/* <div className='flex flex-col gap-2'> */}
          {/* <small>Departemen</small> */}
          {/* {isEditable ? (
              <Controller
                name='departmentId'
                control={control}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl size='small' fullWidth error={!!error}>
                    <Select
                      key={value}
                      fullWidth
                      id='select-departmentId'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      // label='Departemen (Opsional)'
                      size='small'
                      labelId='departmentId-select'
                      inputProps={{ placeholder: 'Pilih Departemen' }}
                      defaultValue=''
                    >
                      {departmentList?.map(department => (
                        <MenuItem key={department.id} value={department.id}>
                          {department.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {error && <FormHelperText>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            ) : (
              <Typography>{!!generalLedgerData?.department ? generalLedgerData.department?.name : '-'}</Typography>
            )} */}
          {/* </div> */}
          {/* <div className='flex flex-col gap-2'> */}
          {/* <small>Label Proyek</small> */}
          {/* {isEditable ? (
              <Controller
                control={control}
                name='projectLabelId'
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl size='small' fullWidth error={!!error}>
                    <Select
                      key={value}
                      fullWidth
                      id='select-projectLabelId'
                      value={value}
                      disabled={!siteIdWatch}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      // label='Label Proyek (Opsional)'
                      labelId='select-projectLabelId'
                      size='small'
                      inputProps={{ placeholder: 'Pilih Label' }}
                    >
                      {projectLabels?.map(label => (
                        <MenuItem key={label.id} value={label.id}>
                          {label.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            ) : (
              <Typography>{!!generalLedgerData?.projectLabel ? generalLedgerData.projectLabel?.name : '-'}</Typography>
            )} */}
          {/* </div> */}
          {/* <div className='flex flex-col gap-2'> */}
          {/* <small>Kode Unit</small> */}
          {/* {isEditable ? (
              <Controller
                control={control}
                name='unitId'
                rules={{
                  validate: value => {
                    return true
                  }
                }}
                render={({ fieldState: { error } }) => (
                  <UnitAutocomplete
                    label=''
                    placeholder=''
                    autoCompleteProps={{ size: 'small' }}
                    fullWidth
                    value={selectedUnit}
                    // label='Kode Unit (Opsional)'
                    onChange={unit => {
                      if (unit) {
                        setValue('unitId', unit.id)
                        setValue('unit', unit)
                        setSelectedUnit(unit)
                      } else {
                        setValue('unitId', undefined)
                        setValue('unit', null)
                        setSelectedUnit(null)
                      }
                    }}
                    error={!!error}
                    helperText={error?.message}
                  />
                )}
              />
            ) : (
              <Typography>{generalLedgerData?.unit?.number ?? '-'}</Typography>
            )} */}
          {/* </div> */}
          {/* <Grid item xs={12}>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Tipe Unit
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <Typography>{generalLedgerData?.unit?.type ?? '-'}</Typography>
          </Grid>
          <Grid item xs={12}>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Merk Unit
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <Typography>{generalLedgerData?.unit?.brandName ?? '-'}</Typography>
          </Grid> */}
          <div className='flex md:col-span-2 flex-col gap-2'>
            <small>Keterangan</small>
            {isEditable ? (
              <Controller
                control={control}
                name='description'
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <TextField
                    size='small'
                    multiline
                    rows={2}
                    fullWidth
                    onChange={onChange}
                    value={value}
                    InputLabelProps={{ shrink: !!value }}
                  />
                )}
              />
            ) : (
              <Typography>{!!generalLedgerData?.description ? generalLedgerData.description : '-'}</Typography>
            )}
          </div>
          <div className='flex md:col-span-2 flex-col gap-2'>
            <small>Referensi</small>
            {!!generalLedgerData?.refDocNumber ? (
              <Link
                to={
                  generalLedgerData?.refDocType === JournalRefDocType.Payment
                    ? `/cash-bank/payments/${generalLedgerData?.refDocId}`
                    : generalLedgerData?.refDocType === JournalRefDocType.CashReceipt
                      ? `/cash-bank/receipt/${generalLedgerData?.refDocId}`
                      : generalLedgerData?.refDocType === JournalRefDocType.SalesInvoice
                        ? `/sales/invoice/list/${generalLedgerData?.refDocId}`
                        : generalLedgerData?.refDocType === JournalRefDocType.PurchaseInvoice
                          ? `/purchase-invoice/list/${generalLedgerData?.refDocId}`
                          : '#'
                }
              >
                <Typography className='text-primary' variant='button'>
                  {generalLedgerData?.refDocNumber ?? '-'}
                </Typography>
              </Link>
            ) : (
              '-'
            )}
          </div>
          <div className='flex md:col-span-2 flex-col gap-2'>
            <small>Nomor Voucher</small>
            <Typography>{generalLedgerData?.voucherNumber ?? '-'}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DetailLedgerCard
