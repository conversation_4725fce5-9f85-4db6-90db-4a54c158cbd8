// MUI Imports
import Grid from '@mui/material/Grid'
import { <PERSON><PERSON>crum<PERSON>, Chip, Typography, Button } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { usePurchaseInvoice, PurchaseInvoiceContextProvider } from '../context/PurchaseInvoiceContext'

import { useUpdatePurchaseInvoiceApproval } from '@/api/services/purchase-invoice/mutation'

// Components
import { statusChipValue } from './components/ApprovalsCard'
import PurchaseInvoiceDetailCard from './components/PurchaseInvoiceDetailCard'
import DocumentCard from './components/DocumentCard'
import PaymentSummaryCard from './components/PaymentSummaryCard'
import OtherExpenseCard from './components/OtherExpenseCard'
import DiscountCard from './components/DiscountCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { toast } from 'react-toastify'
import { PurchaseInvoiceStatus } from '@/types/purchaseInvoiceTypes'
import { useRouter } from '@/routes/hooks'
import DialogDetailJournal from '@/components/dialogs/detail-journal-dialog'
import { useState } from 'react'
import Permission from '@/core/components/Permission'
import PurchaseOrderCard from '@/pages/purchase-invoice/detail/components/PurchaseOrderCard'
import ActivityLogCard from '@/pages/purchase-invoice/detail/components/ActivityLogCard'

const PurchaseInvoiceDetailPage = () => {
  const { userProfile } = useAuth()
  const router = useRouter()
  const { purchaseInvoiceData, fetchPurchaseInvoiceList, purchaseInvoiceLogs } = usePurchaseInvoice()
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null)

  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdatePurchaseInvoiceApproval()

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        piId: purchaseInvoiceData?.id,
        approvalId: formData.approvalId,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchPurchaseInvoiceList()
        },
        onError: error => {
          const message = error?.message
          if (message) {
            toast.error(message)
          } else {
            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
          }
        }
      }
    )
  }

  const handleExport = () => {
    // TODO: Implement export functionality
  }

  const handlePrint = () => {
    // TODO: Implement print functionality
  }

  const handleCreatePayment = () => {
    router.push(`/cash-bank/payments/create?purchaseInvoiceId=${purchaseInvoiceData?.id}`)
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
            </Link>
            <Link to='/cash-bank/purchase-invoice' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Hutang Pembelian</Typography>
            </Link>
            <Typography>Detil Hutang Pembelian</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>No. Faktur: {purchaseInvoiceData?.number}</Typography>
                <Chip
                  label={!!purchaseInvoiceData?.paidAt ? 'Terbayar' : 'Belum Dibayar'}
                  color={!!purchaseInvoiceData?.paidAt ? 'success' : 'default'}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(purchaseInvoiceData?.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 is-full sm:is-auto'>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
                onClick={handleExport}
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-printer-line' />}
                className='is-full sm:is-auto'
                onClick={handlePrint}
              >
                Cetak
              </Button>
              {!!purchaseInvoiceData?.journalId && (
                <Permission permission={['journal.create']}>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-eye-line' />}
                    className='is-full sm:is-auto'
                    onClick={() => setSelectedJournalId(purchaseInvoiceData?.journalId)}
                  >
                    Cek Jurnal
                  </Button>
                </Permission>
              )}
              {purchaseInvoiceData?.status === PurchaseInvoiceStatus.APPROVED && (
                <Button onClick={handleCreatePayment} variant='contained'>
                  Buat Pembayaran
                </Button>
              )}
            </div>
          </div>
        </Grid>

        {/* Left Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Purchase Invoice Detail Card */}
            <Grid item xs={12}>
              <PurchaseInvoiceDetailCard />
            </Grid>
          </Grid>
        </Grid>
        {/* Right Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Document Card */}
            <Grid item xs={12}>
              <DocumentCard />
            </Grid>

            {/* Approvals Card */}
            {(purchaseInvoiceData?.approvals?.length ?? 0) > 0 ? (
              <Grid item xs={12}>
                <ApprovalDetailCard
                  approvalList={purchaseInvoiceData?.approvals ?? []}
                  handleUpdateApprover={handleUpdateApprover}
                  updateApproverLoading={updateApproverLoading}
                />
              </Grid>
            ) : null}
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container gap={4}>
            {/* Purchase Order Card */}
            <Grid item xs={12}>
              <PurchaseOrderCard purchaseInvoiceData={purchaseInvoiceData} />
            </Grid>

            {/* Discount Card */}
            {/* <Grid item xs={12}>
              <DiscountCard />
            </Grid> */}

            {/* Other Expense Card */}
            <Grid item xs={12}>
              <OtherExpenseCard />
            </Grid>

            {/* Payment Summary Card */}
            <Grid item xs={12}>
              <PaymentSummaryCard />
            </Grid>

            <Grid item xs={12}>
              <ActivityLogCard logList={purchaseInvoiceLogs} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {!!selectedJournalId && (
        <DialogDetailJournal
          open={!!selectedJournalId}
          setOpen={open => setSelectedJournalId(!open && null)}
          journalId={selectedJournalId}
        />
      )}
    </>
  )
}

const PurchaseInvoiceApprovalDetailPageWithProvider = () => {
  return (
    <PurchaseInvoiceContextProvider>
      <PurchaseInvoiceDetailPage />
    </PurchaseInvoiceContextProvider>
  )
}

export default PurchaseInvoiceApprovalDetailPageWithProvider
