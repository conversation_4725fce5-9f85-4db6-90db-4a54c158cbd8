import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useReceipt } from '../context/ReceiptContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { getStatusConfig } from '../config/utils'
import CreatedByCard from '../detail/components/CreatedByCard'
import ApprovalsCard from '../approval/components/ApprovalsCard'
import DocInvoiceCard from '../detail/components/DocInvoiceCard'
import InvoiceDetailCard from '../detail/components/InvoiceDetailCard'
import PaymentDetailCard from '../detail/components/PaymentDetailCard'
import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useReadCashReceipt, useReadPaymentApproval } from '@/api/services/cashbank/mutation'
import ItemListCard from '../detail/components/AccountListCard'

const PaymentDetailPage = () => {
  const { cashReceiptData, fetchCashReceiptData } = useReceipt()
  const { userProfile } = useAuth()

  const { mutate: readMutate } = useReadCashReceipt()

  useEffect(() => {
    const ownApproval = cashReceiptData?.approvals?.find(approval => approval.userId === userProfile?.id)
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          isRead: true,
          approvalId: ownApproval.id,
          id: cashReceiptData?.id
        },
        { onSuccess: () => fetchCashReceiptData() }
      )
    }
  }, [cashReceiptData])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Persetujuan</Typography>
          </Link>
          <Link to='/cash-bank/receipts-approval' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Persetujuan Penerimaan</Typography>
          </Link>
          <Typography>Detil Penerimaan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <div className='flex gap-2 items-start'>
              <Typography variant='h4'>No. Penerimaan: {cashReceiptData?.number}</Typography>
              <Chip
                size='small'
                label={getStatusConfig(cashReceiptData?.status).label}
                color={getStatusConfig(cashReceiptData?.status).color as any}
                variant='tonal'
              />
            </div>
            <Typography>
              {formatDate(cashReceiptData?.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}
            </Typography>
          </div>
          <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
            {/* <Button
              disabled={loadingMutate}
              onClick={() => router.back()}
              color='secondary'
              variant='outlined'
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              variant='contained'
              disabled={loadingMutate}
              onClick={handleSubmit(onSubmit)}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button> */}
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {cashReceiptData?.salesInvoiceId && (
            <>
              <Grid item xs={12}>
                <DocInvoiceCard purchaseInvoiceId={cashReceiptData?.salesInvoiceId} />
              </Grid>
              <Grid item xs={12}>
                <InvoiceDetailCard purchaseInvoiceId={cashReceiptData?.salesInvoiceId} />
              </Grid>
            </>
          )}
          <Grid item xs={12}>
            <PaymentDetailCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
          <Grid item xs={12}>
            <ApprovalsCard />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default PaymentDetailPage
