import {
  Chip,
  Icon<PERSON>utton,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent
} from '@mui/material'
import { toCurrency } from '@/utils/helper'
import { SalesInvoice } from '@/types/salesInvoiceTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { statusChipValue } from '@/pages/sales/invoice/approval/config/table'

type InvoiceListCardProps = {
  invoices: SalesInvoice[]
  onRemove: (invoiceId: string) => void
  disabled?: boolean
}

const InvoiceListCard = ({ invoices, onRemove, disabled = false }: InvoiceListCardProps) => {
  if (invoices.length === 0) {
    return null
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Daftar Faktur</Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow className='bg-green-50'>
                  <TableCell className='font-medium text-gray-700'>No. Faktur</TableCell>
                  <TableCell className='font-medium text-gray-700'>Tanggal</TableCell>
                  <TableCell className='font-medium text-gray-700'>Sub Total</TableCell>
                  <TableCell className='font-medium text-gray-700'>Biaya Lain-lain</TableCell>
                  <TableCell className='font-medium text-gray-700'>Total Faktur</TableCell>
                  <TableCell className='font-medium text-gray-700 text-center'>Aksi</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invoices.map(invoice => (
                  <TableRow key={invoice.id} className='hover:bg-gray-50'>
                    <TableCell className='text-gray-600'>
                      <div className='flex flex-col gap-1'>
                        <div className='flex gap-2 items-center'>
                          <Typography variant='body2' className='font-medium'>
                            {invoice?.number || '-'}
                          </Typography>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className='text-gray-600'>
                      <Typography variant='body2'>
                        {formatDate(invoice?.createdAt, 'dd MMM yyyy', { locale: id })}
                      </Typography>
                    </TableCell>
                    <TableCell className='text-gray-600'>
                      <Typography variant='body2' className='font-medium'>
                        {toCurrency(invoice?.subTotalAmount, false, invoice?.currency?.code)}
                      </Typography>
                    </TableCell>
                    <TableCell className='text-gray-600'>
                      <Typography variant='body2' className='font-medium'>
                        {toCurrency(invoice?.otherAmount, false, invoice?.currency?.code)}
                      </Typography>
                    </TableCell>
                    <TableCell className='text-gray-600'>
                      <Typography variant='body2' className='font-semibold text-green-700'>
                        {toCurrency(invoice?.totalAmount, false, invoice?.currency?.code)}
                      </Typography>
                    </TableCell>
                    <TableCell className='text-gray-600 text-center'>
                      <IconButton disabled={disabled} onClick={() => onRemove(invoice.id)} size='small' color='error'>
                        <i className='ri-close-circle-line' />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
        <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
          <Typography color='black' variant='h6'>
            Total Pembayaran
          </Typography>
          <Typography color='black' variant='h5' className='font-semibold'>
            {toCurrency(invoices.reduce((acc, item) => acc + item.totalAmount, 0))}
          </Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceListCard
