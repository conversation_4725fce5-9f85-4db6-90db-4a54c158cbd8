import { Document, Page, View, Text, StyleSheet, Image } from '@react-pdf/renderer'
import { Table, TableHeader, TableCell, TableRow } from '@ag-media/react-pdf-table'
import { ReactNode } from 'react'
import { CashReceiptType, PaymentType } from '../../config/types'
import { formatDate } from 'date-fns'
import { formatThousandSeparator, toCurrency, toTerbilang } from '@/utils/helper'
import { id } from 'date-fns/locale'
import { colors } from '@mui/material'
import { PAGE_SIZE } from '@/utils/constants'

// Styles
export const pdfStyles = StyleSheet.create({
  page: {
    padding: 30,
    color: '#323639'
  },
  textNormal: {
    fontSize: 9
  },
  textHeading: {
    fontSize: 20,
    fontWeight: 700,
    lineHeight: 1.2
  },
  textBold: {
    fontFamily: 'Helvetica-Bold',
    fontWeight: 700,
    fontSize: 9
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 5,
    marginBottom: 20
  },
  section: {
    display: 'flex',
    flexDirection: 'column',
    gap: 3,
    alignItems: 'flex-start',
    justifyContent: 'space-between'
  },
  flexRowBetween: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row'
  },
  flexCol: {
    display: 'flex',
    flexDirection: 'column'
  },
  border: {
    border: '1px solid #000',
    borderRadius: 4,
    padding: 4
  },
  textAddress: {
    fontSize: 8,
    width: '100%'
  },
  poInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15
  },
  table: {
    width: '100%',
    marginBottom: 15,
    border: '1px solid #000'
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTop: '1px solid black'
  },
  tableHeader: {
    fontWeight: 'bold',
    fontSize: 8,
    borderTop: '1px solid black'
  },
  tableCell: {
    fontSize: 8,
    padding: 6,
    border: 'none'
  },
  textRight: {
    textAlign: 'right',
    width: '100%'
  },
  textCenter: {
    textAlign: 'center',
    width: '100%'
  },
  totals: {
    fontSize: 10,
    marginLeft: 'auto',
    width: '30%',
    display: 'flex',
    flexDirection: 'column',
    gap: 4
  },
  signature: {
    marginTop: 20,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  signatureItem: {
    fontSize: 10,
    minHeight: 65,
    maxWidth: 120,
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  signatureName: {
    paddingTop: 4,
    fontSize: 9
  }
})

const headerProduct = ['Account No', 'Account Name', 'Amount', 'Memo']

export const TextItemFlex = ({ children, end, style }: { children: ReactNode; end?: boolean; style?: any }) => (
  <Text
    style={{
      width: '50%',
      fontSize: 9,
      ...(end ? { textAlign: 'right' } : {}),
      ...style
    }}
  >
    {children}
  </Text>
)

const ReceiptPdfDocument = ({
  cashReceipt,
  qrCode,
  isPreview = false
}: {
  cashReceipt: CashReceiptType
  qrCode?: any
  isPreview?: boolean
}) => {
  return (
    <Document>
      <Page size={PAGE_SIZE} style={pdfStyles.page}>
        {/* Header Perusahaan */}
        <View style={[pdfStyles.flexRow, { justifyContent: 'space-between', gap: 4 }]}>
          <View style={[pdfStyles.border, pdfStyles.flexRow, { gap: 8 }]}>
            <Image src='/rpu-logo.png' style={{ width: 32, height: 32 }} />
            <View style={[pdfStyles.flexCol]}>
              <Text style={[pdfStyles.textBold, { color: '#000' }]}>PT. RIMBA PERKASA UTAMA</Text>
              <View style={{ marginVertical: 2 }} />
              <Text style={pdfStyles.textAddress}>Jl. Danau Toba No. 148</Text>
              <Text style={[pdfStyles.textAddress, { marginTop: 2 }]}>Samarinda</Text>
            </View>
          </View>
          <Text style={[pdfStyles.textBold, { fontSize: 14, color: '#000', paddingHorizontal: 8 }]}>
            Bukti Penerimaan
          </Text>
        </View>

        <View style={{ marginVertical: 14 }} />

        <View style={[pdfStyles.flexCol, { gap: 4 }]}>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Nomor Penerimaan</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>{cashReceipt?.number}</TextItemFlex>
          </View>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Tanggal</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>
              {formatDate(cashReceipt?.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}
            </TextItemFlex>
          </View>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Departemen</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>{cashReceipt?.department?.name ?? '-'}</TextItemFlex>
          </View>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Nomor Voucher</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>{cashReceipt?.voucherNumber ?? '-'}</TextItemFlex>
          </View>
        </View>

        <View style={{ marginVertical: 10, borderTop: '1px solid #323639' }} />

        <View style={[pdfStyles.flexRow, { fontSize: 10, gap: 8 }]}>
          <View style={[pdfStyles.flexCol, { flexGrow: 1 }]}>
            <Text style={pdfStyles.textNormal}>Diterima di Akun:</Text>
            <View style={{ marginVertical: 2 }} />
            <Text style={pdfStyles.textBold}>
              {cashReceipt?.account?.code ?? '-'} {cashReceipt?.account?.name ?? '-'}
            </Text>
            <Text style={{ marginTop: 2, ...pdfStyles.textNormal }}></Text>
          </View>
          {cashReceipt?.salesInvoiceId && (
            <View style={[pdfStyles.flexCol, { flexGrow: 1 }]}>
              <Text style={pdfStyles.textNormal}>No Faktur:</Text>
              <Text style={[pdfStyles.textBold, { marginTop: 4 }]}>{cashReceipt?.salesInvoice?.number ?? '-'}</Text>
              <View style={{ marginVertical: 2 }} />
            </View>
          )}
        </View>

        <View style={{ marginVertical: 10 }} />

        {/* Tabel Items */}
        <Table
          tdStyle={{ padding: '10px', textAlign: 'center' }}
          style={pdfStyles.table}
          weightings={[0.8, 1.1, 0.5, 0.8, 0.3, 0.7, 1.1]}
        >
          <TableHeader fixed style={pdfStyles.tableHeader}>
            {headerProduct.map(header => (
              <TableCell key={`$indexHeader}`} style={[pdfStyles.tableCell, pdfStyles.textBold]}>
                <Text style={['Qty', 'Harga', 'Diskon', 'Total'].includes(header) ? pdfStyles.textCenter : {}}>
                  {header}
                </Text>
              </TableCell>
            ))}
          </TableHeader>

          {cashReceipt?.items.map((item, index) => (
            <TableRow
              key={index}
              style={[
                pdfStyles.tableRow,
                { borderBottom: index === cashReceipt?.items?.length - 1 ? '1px solid #000' : 'none' },
                { borderTop: index === 0 ? '1px solid #000' : '0.5px solid #000' }
              ]}
            >
              <TableCell style={[pdfStyles.tableCell]}>
                <Text>{item?.account?.code}</Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text style={{ textAlign: 'left' }}>{item?.account?.name}</Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell, { paddingRight: 20 }]}>
                <Text style={[pdfStyles.textRight]}>{formatThousandSeparator(item?.amount)}</Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text>{item?.description}</Text>
              </TableCell>
            </TableRow>
          ))}
        </Table>

        <View style={[pdfStyles.flexRowBetween, { gap: 4 }]}>
          <View style={{ flexGrow: 3 }}>
            <View
              style={{
                fontSize: 10,
                display: 'flex',
                alignItems: 'flex-start',
                gap: 4,
                flexDirection: 'row',
                maxWidth: '100%',
                marginBottom: 6
              }}
            >
              <Text style={pdfStyles.textNormal}>Terbilang:</Text>
              <Text style={pdfStyles.textNormal}>
                {cashReceipt?.totalAmount ? toTerbilang(cashReceipt?.totalAmount) : '-'}{' '}
                {cashReceipt?.currency?.name.toLowerCase()}
              </Text>
            </View>
            <View
              style={{
                fontSize: 10,
                display: 'flex',
                alignItems: 'flex-start',
                gap: 4,
                flexDirection: 'row',
                maxWidth: '50%'
              }}
            >
              <Text style={pdfStyles.textNormal}>Catatan:</Text>
              <Text style={pdfStyles.textNormal}>{cashReceipt?.memo ?? '-'}</Text>
            </View>
          </View>
          <View style={[pdfStyles.totals, { flexGrow: 1 }]}>
            <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
              <TextItemFlex>Sub Total</TextItemFlex>
              <Text style={pdfStyles.textNormal}>:</Text>
              <TextItemFlex end>
                {toCurrency(
                  cashReceipt?.items?.reduce((acc, item) => acc + item?.amount, 0) || 0,
                  true,
                  cashReceipt?.currency?.code
                )}
              </TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
              <TextItemFlex>Diskon Pembelian</TextItemFlex>
              <Text style={pdfStyles.textNormal}>:</Text>
              <TextItemFlex end>
                {toCurrency(cashReceipt?.discountAmount ?? 0, true, cashReceipt?.currency?.code)}
              </TextItemFlex>
            </View>
            <View>
              <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
                <TextItemFlex
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                >
                  Total Penerimaan
                </TextItemFlex>
                <Text
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                >
                  :
                </Text>
                <TextItemFlex
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                  end
                >
                  {toCurrency(cashReceipt?.totalAmount, true, cashReceipt?.currency?.code)}
                </TextItemFlex>
              </View>
            </View>
          </View>
        </View>
        <View style={{ marginVertical: 5 }} />
        <View style={[pdfStyles.signature, { justifyContent: 'flex-start' }]}>
          <View style={pdfStyles.signatureItem}>
            <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>Dibuat oleh</Text>
            <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
            <Text style={pdfStyles.signatureName}>{cashReceipt?.createdByUser?.fullName}</Text>
          </View>
          {!isPreview &&
            cashReceipt?.approvals?.map((app, index) => (
              <View key={index} style={pdfStyles.signatureItem}>
                <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>{index === 0 ? 'Disetujui oleh' : ''}</Text>
                <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
                <Text style={pdfStyles.signatureName}>{app?.user?.fullName}</Text>
              </View>
            ))}
        </View>
        {isPreview ? (
          <View fixed style={{ top: -220, bottom: 0, left: '20%', right: '50%' }}>
            <Text style={{ color: colors.blueGrey[700], opacity: 0.4, fontWeight: 800, fontSize: 54 }}>
              P R E V I E W
            </Text>
          </View>
        ) : null}
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-end',
            padding: 16,
            width: '100%'
          }}
        >
          {qrCode && <Image src={qrCode} style={{ width: 64, height: 64 }} />}
        </View>
      </Page>
    </Document>
  )
}

export default ReceiptPdfDocument
