import SellingInvoiceQueryMethods from '@/api/services/sales-invoice/query'
import { useRouter } from '@/routes/hooks'
import { Card, CardContent, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  purchaseInvoiceId?: string
}

const DocInvoiceCard = ({ purchaseInvoiceId }: Props) => {
  const router = useRouter()
  const { data: salesInvoiceData } = useQuery({
    queryKey: ['SALES_INVOICE_DETAIL_QUERY_KEY', purchaseInvoiceId],
    queryFn: () => SellingInvoiceQueryMethods.getSalesInvoice(purchaseInvoiceId)
  })
  return (
    <Card>
      <CardContent className='flex flex-col gap-2'>
        <small>Dibuat dari <PERSON></small>
        <Typography
          variant='h5'
          className='text-primary cursor-pointer'
          onClick={() => router.push(`/sales/invoice/list/${salesInvoiceData?.id}`)}
        >
          No. Faktur: {salesInvoiceData?.number}
        </Typography>
        <Typography variant='caption'>
          {salesInvoiceData?.createdAt ? formatDate(salesInvoiceData.createdAt, 'dd MMM yyyy', { locale: id }) : '-'}
        </Typography>
      </CardContent>
    </Card>
  )
}

export default DocInvoiceCard
