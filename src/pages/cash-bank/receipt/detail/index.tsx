import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typo<PERSON> } from '@mui/material'
import { Link } from 'react-router-dom'
import { useReceipt } from '../context/ReceiptContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { getStatusConfig } from '../config/utils'
import CreatedByCard from './components/CreatedByCard'
import ApprovalDetailCard from './components/ApprovalDetailCard'
import DocInvoiceCard from './components/DocInvoiceCard'
import InvoiceDetailCard from './components/InvoiceDetailCard'
import PaymentDetailCard from './components/PaymentDetailCard'
import ItemListCard from './components/AccountListCard'
import { useRouter } from '@/routes/hooks'
import { useState } from 'react'
import DialogDetailJournal from '@/components/dialogs/detail-journal-dialog'
import Permission from '@/core/components/Permission'
import ReceiptPdfDocument from './components/PdfDocument'
import { useAuth } from '@/contexts/AuthContext'
import { saveAs } from 'file-saver'
import { pdf } from '@react-pdf/renderer'

const ReceiptDetailPage = () => {
  const router = useRouter()
  const { ownSiteList } = useAuth()
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null)
  const { cashReceiptData } = useReceipt()

  const handleExport = async () => {
    const blob = await pdf(
      <ReceiptPdfDocument
        cashReceipt={{
          ...cashReceiptData
        }}
      />
    ).toBlob()
    const filename = cashReceiptData?.number + '.pdf'
    saveAs(blob, filename)
  }

  const handlePrint = async () => {
    const blob = await pdf(
      <ReceiptPdfDocument
        cashReceipt={{
          ...cashReceiptData
        }}
      />
    ).toBlob()
    const url = URL.createObjectURL(blob)
    const printWindow = window.open(url, '_blank')
    printWindow.onload = () => {
      printWindow.print()
      printWindow.onafterprint = () => {
        printWindow.close()
      }
    }
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
            </Link>
            <Link to='/cash-bank/receipt/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Penerimaan</Typography>
            </Link>
            <Typography>Detil Dokumen</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <div className='flex gap-2 items-start'>
                <Typography variant='h4'>No. Penerimaan: {cashReceiptData?.number}</Typography>
                <Chip
                  size='small'
                  label={getStatusConfig(cashReceiptData?.status).label}
                  color={getStatusConfig(cashReceiptData?.status).color as any}
                  variant='tonal'
                />
              </div>
              <Typography>
                {formatDate(cashReceiptData?.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}
              </Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button
                // disabled={loadingMutate}
                onClick={() => handleExport()}
                startIcon={<i className='ri-upload-2-line' />}
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                variant='contained'
                startIcon={<i className='ic-outline-local-printshop' />}
                // disabled={loadingMutate}
                onClick={handlePrint}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button>
              {cashReceiptData?.journalId && (
                <Permission permission={['journal.create']}>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-eye-line' />}
                    className='is-full sm:is-auto'
                    onClick={() => setSelectedJournalId(cashReceiptData?.journalId)}
                  >
                    Cek Jurnal
                  </Button>
                </Permission>
              )}
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {cashReceiptData?.salesInvoiceId && (
              <>
                <Grid item xs={12}>
                  <DocInvoiceCard purchaseInvoiceId={cashReceiptData?.salesInvoiceId} />
                </Grid>
                <Grid item xs={12}>
                  <InvoiceDetailCard purchaseInvoiceId={cashReceiptData?.salesInvoiceId} />
                </Grid>
              </>
            )}
            <Grid item xs={12}>
              <PaymentDetailCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <CreatedByCard />
            </Grid>
            <Grid item xs={12}>
              <ApprovalDetailCard approvalList={cashReceiptData?.approvals ?? []} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {!!selectedJournalId && (
        <DialogDetailJournal
          open={!!selectedJournalId}
          setOpen={open => setSelectedJournalId(!open && null)}
          journalId={selectedJournalId}
        />
      )}
    </>
  )
}

export default ReceiptDetailPage
