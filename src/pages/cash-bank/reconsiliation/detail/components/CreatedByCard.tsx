import { Ava<PERSON>, Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useReconsiliation } from '../../context/ReconsiliationContext'

const CreatedByCard = () => {
  const { reconciliationData } = useReconsiliation()

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Dibuat Oleh</Typography>
        </div>
        <div className='flex gap-3 items-center w-full px-6'>
          <Avatar src={reconciliationData?.createdByUser?.profilePictureUrl}>J</Avatar>
          <div className='flex flex-col flex-1 gap-0 items-start relative bg-transparent'>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <p className='tracking-[0.2px] leading-6 text-base text-black dark:text-inherit'>
                {reconciliationData?.createdByUser?.fullName ?? '-'}
              </p>
            </div>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <Typography variant='caption'>{reconciliationData?.createdByUser?.title ?? '-'}</Typography>
            </div>
          </div>
          <Typography>
            {formatDate(reconciliationData?.createdAt ? reconciliationData.createdAt : new Date(), 'dd MMM yyyy', {
              locale: id
            })}
          </Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default CreatedByCard
