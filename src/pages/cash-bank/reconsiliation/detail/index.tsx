import { Breadcrumbs, Grid, Typography } from '@mui/material'
import ReconciliationFormCard from './components/ReconciliationFormCard'
import ReconciliationCardResult from './components/ReconciliationResultCard'
import ReconciliationSummary from './components/ReconciliationSummaryCard'
import { useReconsiliation } from '../context/ReconsiliationContext'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { Link } from 'react-router-dom'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import CreatedByCard from './components/CreatedByCard'

const ReconsiliationPage = () => {
  const { setConfirmState } = useMenu()
  const { reconciliationData } = useReconsiliation()

  return (
    <Grid className='relative' container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#'>
            <Typography color='var(--color-text-disabled)'>Kas & Bank</Typography>
          </Link>
          <Link to='/reconciliation'>
            <Typography color='var(--color-text-disabled)'>Rekonsiliasi Bank</Typography>
          </Link>
          <Typography>Buat Rekonsiliasi</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>No Rekonsiliasi: {reconciliationData?.number}</Typography>
            <Typography>
              {reconciliationData?.createdAt
                ? formatDate(reconciliationData.createdAt, 'dd MMM yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <ReconciliationFormCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <CreatedByCard />
      </Grid>
      <Grid item xs={12}>
        <ReconciliationCardResult />
      </Grid>
      <Grid item xs={12} className='sticky bottom-2'>
        <ReconciliationSummary />
      </Grid>
    </Grid>
  )
}

export default ReconsiliationPage
