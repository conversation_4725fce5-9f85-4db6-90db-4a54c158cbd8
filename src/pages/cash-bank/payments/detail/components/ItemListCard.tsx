// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'

// Utils
import { formatThousandSeparator, toCurrency } from '@/utils/helper'

// Context
import { usePayment } from '../../context/PaymentContext'

type Props = {}

const ItemListCard = ({}: Props) => {
  const { paymentData } = usePayment()

  if (!paymentData?.items?.length) {
    return (
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>List Item</Typography>
          </div>
          <div className='flex flex-col gap-4'>
            <Typography variant='body2' color='text.secondary'>
              Tidak ada item pembayaran
            </Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>List Item</Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow className='bg-green-50'>
                  <TableCell className='font-medium text-gray-700'>AKUN PERKIRAAN</TableCell>
                  <TableCell className='font-medium text-gray-700'>DESKRIPSI</TableCell>
                  <TableCell className='font-medium text-gray-700'>NOMINAL</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paymentData.items.map((item, index) => (
                  <TableRow key={item.id || index} className='hover:bg-gray-50'>
                    <TableCell className='text-gray-600'>
                      [{item.account?.code || '-'}] {item.account?.name || 'Unknown Account'}
                    </TableCell>
                    <TableCell className='text-gray-600'>{item.description || '-'}</TableCell>
                    <TableCell className='text-gray-600'>{formatThousandSeparator(item.amount || 0)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </div>
      </CardContent>
    </Card>
  )
}

export default ItemListCard
