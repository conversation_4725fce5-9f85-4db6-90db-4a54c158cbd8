import { createColumnHelper } from '@tanstack/react-table'
import { CreatePaymentItemPayload } from '../../config/types'
import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'
import { Icon<PERSON>utton, Chip } from '@mui/material'
import { PurchaseInvoice } from '@/types/purchaseInvoiceTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { statusChipValue } from '@/pages/purchase-invoice/detail/components/ApprovalsCard'

const columnHelper = createColumnHelper<CreatePaymentItemPayload>()
const invoiceColumnHelper = createColumnHelper<PurchaseInvoice>()

type RowActionType = {
  delete: (index: number) => void
  edit: (index: number) => void
}

type InvoiceRowActionType = {
  delete: (invoiceId: string) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('code', {
    header: 'Kode Akun',
    cell: ({ row }) => row.original.code
  }),
  columnHelper.accessor('name', {
    header: 'Nama Akun',
    cell: ({ row }) => row.original.name
  }),
  columnHelper.accessor('amount', {
    header: 'Nominal',
    cell: ({ row }) =>
      row.original?.amount < 0
        ? `(${formatThousandSeparator(Math.abs(row.original.amount))})`
        : formatThousandSeparator(row.original.amount)
  }),
  columnHelper.accessor('description', {
    header: 'Memo',
    cell: ({ row }) => row.original?.description ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <div className='flex items-center gap-0.5'>
          <IconButton onClick={() => rowAction.edit(row.index)}>
            <i className='ri-edit-line text-textSecondary' />
          </IconButton>
          <IconButton onClick={() => rowAction.delete(row.index)}>
            <i className='ri-delete-bin-line text-textSecondary' />
          </IconButton>
        </div>
      )
    },
    enableSorting: false
  })
]

export const selectedInvoiceTableColumns = (rowAction: InvoiceRowActionType) => [
  invoiceColumnHelper.accessor('number', {
    header: 'No. Faktur',
    cell: ({ row }) => (
      <div className='flex gap-2 items-center'>
        <span>{row.original.number}</span>
      </div>
    )
  }),
  invoiceColumnHelper.accessor('createdAt', {
    header: 'Tanggal',
    cell: ({ row }) => formatDate(row.original.createdAt, 'dd MMM yyyy', { locale: id })
  }),
  invoiceColumnHelper.accessor('subTotalAmount', {
    header: 'Sub Total',
    cell: ({ row }) => toCurrency(row.original.subTotalAmount, false, row.original.currency?.code)
  }),
  invoiceColumnHelper.accessor('otherAmount', {
    header: 'Biaya Lain-lain',
    cell: ({ row }) => toCurrency(row.original.otherAmount, false, row.original.currency?.code)
  }),
  invoiceColumnHelper.accessor('totalAmount', {
    header: 'Total Faktur',
    cell: ({ row }) => (
      <span className='font-semibold'>{toCurrency(row.original.totalAmount, false, row.original.currency?.code)}</span>
    )
  }),
  invoiceColumnHelper.display({
    id: 'action',
    header: 'Aksi',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.delete(row.original.id)}>
          <i className='ri-close-circle-line text-textSecondary' />
        </IconButton>
      )
    },
    enableSorting: false
  })
]
