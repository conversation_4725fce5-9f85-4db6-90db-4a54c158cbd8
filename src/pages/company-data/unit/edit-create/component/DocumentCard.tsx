import { DocumentUnitType } from '@/types/companyTypes'
import { extractNameFromUrl } from '@/utils/string'
import { Button, Card, CardContent, FormControl, TextField, Typography } from '@mui/material'
import { formatDate, formatISO, toDate } from 'date-fns'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { AddUnitInput, DocObjectType, useFormUnit } from '../../context/FormUnitContext'
import { useFilePicker } from 'use-file-picker'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { useEffect } from 'react'
import { addInsuranceItem } from '../../config/utils'

type DocumentProps = {
  documents?: DocObjectType
}

const DocumentCard = ({ document, index }: { document: DocObjectType; index: number }) => {
  const { isLoading, setDocsList, docsList } = useFormUnit()
  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    readAs: 'DataURL'
  })
  const { control, resetField } = useFormContext<AddUnitInput>()
  const { update, fields } = useFieldArray({ control, name: 'documents' })

  const onChange = (value: string, key: string) => {
    setDocsList(current => {
      const tempCurrent = [...current]
      tempCurrent[index] = { ...tempCurrent[index], [key]: value }
      return tempCurrent
    })
  }

  useEffect(() => {
    if (filesContent?.length > 0) {
      onChange(filesContent[0].content, 'content')
      onChange(filesContent[0].name, 'name')
    }
  }, [filesContent])

  return (
    <div key={index} className='flex flex-col gap-2'>
      <div className='flex gap-2 items-center justify-between'>
        <Typography>{document.type}</Typography>
      </div>
      <div className='flex flex-col gap-2'>
        <Typography variant='caption' color='text-secondary'>
          Dokumen {document.type}
        </Typography>
        <div className='flex flex-col gap-2 flex-1'>
          <div className='flex items-center gap-4'>
            <TextField
              key={JSON.stringify({ filesContent, document })}
              label='Unggah Dokumen'
              fullWidth
              value={filesContent?.[0]?.name ?? document?.name}
              placeholder='Belum ada file dipilih'
              aria-readonly
              className='flex-1'
            />
            <Button disabled={isLoading} variant='contained' onClick={() => openFilePicker()}>
              Unggah
            </Button>
          </div>
        </div>
      </div>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
        <div className='flex flex-col gap-2'>
          <AppReactDatepicker
            boxProps={{ className: 'is-full' }}
            selected={docsList[index]?.effectiveDate ? toDate(docsList[index]?.effectiveDate) : undefined}
            onChange={(date: Date) => onChange(formatISO(date), 'effectiveDate')}
            dateFormat='dd MMM yyyy'
            customInput={
              <TextField
                fullWidth
                label='Tanggal Mulai Berlaku'
                className='flex-1'
                InputProps={{
                  readOnly: true
                }}
              />
            }
            required={!!(filesContent?.[0]?.name ?? document?.name)}
          />
        </div>
        <div className='flex flex-col gap-2'>
          <AppReactDatepicker
            boxProps={{ className: 'is-full' }}
            selected={docsList[index]?.expirationDate ? toDate(docsList[index]?.expirationDate) : undefined}
            onChange={(date: Date) => onChange(formatISO(date), 'expirationDate')}
            dateFormat='dd MMM yyyy'
            customInput={
              <TextField
                fullWidth
                label='Tanggal Habis Berlaku'
                className='flex-1'
                InputProps={{
                  readOnly: true
                }}
              />
            }
            required={!!(filesContent?.[0]?.name ?? document?.name)}
          />
        </div>
      </div>
    </div>
  )
}

const InsuranceCard = ({
  insurance,
  index,
  onDelete
}: {
  insurance: DocObjectType
  index: number
  onDelete?: (index: number) => void
}) => {
  const { isLoading, setInsuranceList, insuranceList } = useFormUnit()
  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    readAs: 'DataURL'
  })

  const onChange = (value: string, key: string) => {
    setInsuranceList(current => {
      const tempCurrent = [...current]
      tempCurrent[index] = { ...tempCurrent[index], [key]: value }
      return tempCurrent
    })
  }

  useEffect(() => {
    if (filesContent?.length > 0) {
      onChange(filesContent[0].content, 'content')
    }
  }, [filesContent])
  return (
    <div className='p-4 bg-[#4C4E640D] flex flex-col gap-2'>
      <div className='flex justify-between items-center'>
        <Typography>Asuransi {index + 1}</Typography>
        {index > 0 && (
          <Button variant='text' color='error' size='small' onClick={() => onDelete?.(index)}>
            Hapus
          </Button>
        )}
      </div>
      <div className='flex flex-col gap-2'>
        <FormControl fullWidth>
          <TextField
            key={insuranceList[index]?.name}
            label='Nama Asuransi'
            className='bg-white rounded-xl'
            defaultValue={insuranceList[index]?.name}
            onBlur={e => onChange(e.target.value, 'name')}
            required={!!(filesContent?.[0]?.name ?? insurance?.name)}
          />
        </FormControl>
      </div>
      <div className='flex flex-col gap-2'>
        <div className='flex flex-col gap-2 flex-1'>
          <div className='flex items-center gap-4'>
            <TextField
              key={JSON.stringify({ filesContent, insurance })}
              label='Unggah Dokumen'
              fullWidth
              value={filesContent?.[0]?.name ?? insurance?.name}
              placeholder='Belum ada file dipilih'
              aria-readonly
              className='flex-1 bg-white rounded-xl'
            />
            <Button disabled={isLoading} variant='contained' onClick={() => openFilePicker()}>
              Unggah
            </Button>
          </div>
        </div>
      </div>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
        <div className='flex flex-col gap-2'>
          <AppReactDatepicker
            className='bg-white rounded-xl'
            boxProps={{ className: 'is-full' }}
            selected={insuranceList[index]?.effectiveDate ? toDate(insuranceList[index]?.effectiveDate) : undefined}
            onChange={(date: Date) => onChange(formatISO(date), 'effectiveDate')}
            dateFormat='dd MMM yyyy'
            customInput={
              <TextField
                fullWidth
                label='Tanggal Mulai Berlaku'
                className='flex-1'
                InputProps={{
                  readOnly: true
                }}
              />
            }
            required={!!(filesContent?.[0]?.name ?? insurance?.name)}
          />
        </div>
        <div className='flex flex-col gap-2'>
          <AppReactDatepicker
            className='bg-white rounded-xl'
            boxProps={{ className: 'is-full' }}
            selected={insuranceList[index]?.expirationDate ? toDate(insuranceList[index]?.expirationDate) : undefined}
            onChange={(date: Date) => onChange(formatISO(date), 'expirationDate')}
            dateFormat='dd MMM yyyy'
            customInput={
              <TextField
                fullWidth
                label='Tanggal Habis Berlaku'
                className='flex-1'
                InputProps={{
                  readOnly: true
                }}
              />
            }
            required={!!(filesContent?.[0]?.name ?? insurance?.name)}
          />
        </div>
      </div>
    </div>
  )
}

const DocumentCards = (props: DocumentProps) => {
  const { docsList, insuranceList, setInsuranceList } = useFormUnit()

  const onAddInsurance = () => {
    setInsuranceList(current => [...current, addInsuranceItem(insuranceList)])
  }

  const onDeleteInsurance = (index: number) => {
    setInsuranceList(current => {
      const tempCurrent = [...current]
      tempCurrent.splice(index, 1)
      return tempCurrent
    })
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Dokumen Unit</Typography>
        </div>
        <div className='space-y-4'>
          {docsList.map((document, index) => (
            <DocumentCard document={document} index={index} />
          ))}
        </div>
        <Typography>Asuransi (Opsional)</Typography>
        <div className='space-y-4'>
          {insuranceList.map((insurance, index) => (
            <InsuranceCard onDelete={onDeleteInsurance} insurance={insurance} index={index} />
          ))}
        </div>
        <Button size='small' variant='outlined' color='primary' onClick={onAddInsurance}>
          Tambah Asuransi
        </Button>
      </CardContent>
    </Card>
  )
}

export default DocumentCards
