import { ProjectType } from '@/types/projectTypes'
import { toTitleCase } from '@/utils/helper'
import { Chip, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { projectStatusConfig } from './utils'

const columnHelper = createColumnHelper<ProjectType>()

type RowAction = {
  detail: (row: ProjectType) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('code', {
    header: 'Kode Proyek',
    cell: ({ row }) => (
      <Typography
        sx={{ cursor: 'pointer' }}
        color='primary'
        role='button'
        onClick={() => rowAction.detail(row.original)}
      >
        {row.original?.code}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: '<PERSON>a <PERSON>'
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        size='small'
        variant='tonal'
        label={projectStatusConfig[row.original?.status]?.label}
        color={projectStatusConfig[row.original?.status]?.color as any}
      />
    )
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => formatDate(new Date(row.original?.createdAt), 'dd MMM yyyy, HH:mm', { locale: id })
  }),
  columnHelper.display({
    id: 'view',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <Grid container>
          <Grid item xs={12}>
            <IconButton onClick={() => rowAction.detail(row.original)}>
              <i className='ri-eye-line' />
            </IconButton>
          </Grid>
        </Grid>
      )
    }
  })
]
