// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useItem } from '../../context/GoodsItemContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const CreatorCard = () => {
  const { itemData } = useItem()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Dibuat oleh</Typography>
        </div>
        <div className='flex gap-3 items-center w-full'>
          <div className='flex flex-col flex-1 gap-0 items-start relative bg-transparent'>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <p className='tracking-[0.2px] leading-6 text-base text-black dark:text-inherit'>
                {itemData?.createdByUser?.fullName}
              </p>
            </div>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <Typography variant='caption'>{itemData?.createdByUser?.title}</Typography>
            </div>
          </div>
          <Typography>{formatDate(itemData?.createdAt ?? Date.now(), 'dd MMM yyyy, HH:mm', { locale: id })}</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default CreatorCard
