import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useFormatPreRelease } from '../context/FormatPreReleaseContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import FormatDetailCard from './component/FormatDetailCard'
import CheckPointsCard from './component/CheckPointsCard'
import UnitCheckCard from './component/UnitCheckCard'
import CreatedByCard from './component/CreatedByCard'

const DetilFormat = () => {
  const { selectedFormat } = useFormatPreRelease()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Pre-Release</Typography>
          </Link>
          <Link to='/wo/format-pre-release' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Format Pre-Release</Typography>
          </Link>
          <Typography>Detil Format</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>{selectedFormat?.title}</Typography>
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {selectedFormat?.createdAt
                ? formatDate(new Date(selectedFormat.createdAt), 'dd MMM yyyy', {
                    locale: id
                  })
                : '-'}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                color='secondary'
              >
                Ekspor
              </Button>
              <Button
                startIcon={<i className='ic-outline-local-printshop' />}
                variant='outlined'
                color='secondary'
              >
                Cetak
              </Button> */}
            </div>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <FormatDetailCard />
          </Grid>
          <Grid item xs={12}>
            <CheckPointsCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitCheckCard />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default DetilFormat
