import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { getStatusConfig } from '../wo-list/config/utils'
import { usePreRelease } from '../context/PreReleaseContext'
import { WoStatus } from '@/types/woTypes'
import { useQuery } from '@tanstack/react-query'
import { PRE_RELEASE_QUERY_KEY, PRE_RELEASE_QUERY_LIST_KEY } from '@/api/services/pre-release/service'
import PreReleaseQueryMethods from '@/api/services/pre-release/query'
import PreReleaseDocCard from '../../wo/wo-detail/component/PreReleaseDocCard'
import { PreReleaseType } from '@/types/preReleaseTypes'
import { useState } from 'react'
import CreatedByCard from '../../wo/wo-detail/component/CreatedByCard'
import DataUnitDetail from '../../wo/wo-detail/component/DataUnitDetail'
import UnitDetail from '../../wo/wo-detail/component/UnitDetail'
import CardSegment from '../../wo/wo-detail/component/CardSegment'
import CardAnalisis from '../../wo/wo-detail/component/CardAnalisis'
import CardProblem from '../../wo/wo-detail/component/CardProblem'
import CardSchedule from '../../wo/wo-detail/component/CardSchedule'
import ActivityLogCard from '../../wo/wo-detail/component/ActivityLogCard'
import LocationCard from '../../wo/wo-detail/component/LocationCard'
import DialogDetilPreRelease from '@/components/dialogs/detail-pre-release-docs'
import DetailUnitTaking from '@/components/dialogs/detil-unit-taking'
import CompanyQueryMethods, { UNIT_QUERY_KEY } from '@/api/services/company/query'
import { useWo } from '../../wo/context/WoContext'
import { toast } from 'react-toastify'
import RnMQueryMethods from '@/api/services/rnm/query'
import { FR_DETAIL_QUERY_KEY } from '@/api/services/rnm/service'

const UnitTakingDetail = () => {
  const { woDetail, woLogs, refetchWoDetail: refetchWoDetailPreRelease } = usePreRelease()
  const { refetchWoDetail } = useWo()

  const [{ state: preReleaseState, selectedPreRelease }, setPreReleaseState] = useState<{
    state: boolean
    selectedPreRelease: PreReleaseType | null
  }>({
    state: false,
    selectedPreRelease: null
  })
  const [openUnitTaking, setOpenUnitTaking] = useState(false)

  const { data: fieldReportData } = useQuery({
    enabled: !!woDetail?.fieldReport?.id,
    queryKey: [FR_DETAIL_QUERY_KEY, woDetail?.fieldReport?.id],
    queryFn: () => RnMQueryMethods.getFrDetail(woDetail?.fieldReport?.id)
  })

  const { data: unitDetail } = useQuery({
    enabled: !!woDetail?.unitId && openUnitTaking,
    queryKey: [UNIT_QUERY_KEY, woDetail?.unitId],
    queryFn: async () => CompanyQueryMethods.getUnit(woDetail?.unitId)
  })

  const { data: findPreRelease } = useQuery({
    enabled: !!woDetail?.id,
    queryKey: [PRE_RELEASE_QUERY_LIST_KEY, woDetail?.id],
    queryFn: async () => {
      const res = await PreReleaseQueryMethods.getPreReleases({ workOrderId: woDetail?.id })
      return res.items?.[0] ?? null
    },
    placeholderData: null
  })

  const { data: preReleaseDetail, refetch: refetchPreRelease } = useQuery({
    enabled: !!findPreRelease?.id,
    queryKey: [PRE_RELEASE_QUERY_KEY, findPreRelease?.id],
    queryFn: () => PreReleaseQueryMethods.getOnePreRelease(findPreRelease?.id)
  })

  const handleClickDoc = () => {
    setPreReleaseState({ state: true, selectedPreRelease: preReleaseDetail })
  }
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Order</Typography>
          </Link>
          <Link to='/wo/unit-taking' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Pengambilan Unit</Typography>
          </Link>
          <Typography>Detil Work Order</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>No. WO: {woDetail?.number ?? '-'}</Typography>
              <Chip
                label={getStatusConfig(woDetail?.status).label}
                color={getStatusConfig(woDetail?.status).color as any}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(woDetail?.createdAt ? new Date(woDetail.createdAt) : new Date(), 'dd MMM yyyy HH:mm', {
                locale: id
              })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              <Button
                onClick={() => setOpenUnitTaking(true)}
                variant='contained'
                disabled={woDetail?.status !== WoStatus.READY_TO_RELEASE}
              >
                Proses Pengambilan Unit
              </Button>
            </div>
          </div>
        </div>
      </Grid>
      {woDetail?.status === WoStatus.READY_TO_RELEASE && findPreRelease && (
        <Grid item xs={12}>
          <PreReleaseDocCard onClickDoc={handleClickDoc} />
        </Grid>
      )}
      <Grid item xs={12}>
        <CardAnalisis />
      </Grid>
      <Grid item xs={12}>
        <CardSegment />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitDetail unitData={woDetail?.unit} />
          </Grid>
          <Grid item xs={12}>
            <DataUnitDetail />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <LocationCard frData={fieldReportData} />
          </Grid>
          <Grid item xs={12}>
            <CardProblem frDetail={fieldReportData} />
          </Grid>
          <Grid item xs={12}>
            <CardSchedule />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={woLogs} />
          </Grid>
        </Grid>
      </Grid>
      {preReleaseState && (
        <DialogDetilPreRelease
          open={preReleaseState}
          setOpen={bool => setPreReleaseState(curr => ({ ...curr, state: bool }))}
          preReleaseData={selectedPreRelease}
        />
      )}
      {openUnitTaking && (
        <DetailUnitTaking
          open={openUnitTaking}
          setOpen={setOpenUnitTaking}
          unit={unitDetail}
          woId={woDetail?.id}
          onSuccessReleaseCb={() => {
            toast.success('Proses pengambilan unit berhasil')
            refetchWoDetail()
            refetchWoDetailPreRelease()
          }}
        />
      )}
    </Grid>
  )
}

export default UnitTakingDetail
