import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import UnitDetail from './component/UnitDetail'
import DataUnitDetail from './component/DataUnitDetail'
import CreatedByCard from './component/CreatedByCard'
import CardProblem from './component/CardProblem'
import CardSchedule from './component/CardSchedule'
import ActivityLogCard from './component/ActivityLogCard'
import LocationCard from './component/LocationCard'
import { useWo } from '../context/WoContext'
import { getStatusConfig } from '../fr-list/config/utils'
import { FieldReportStatus } from '@/types/frTypes'
import CloseReason from './component/CloseReasonCard'

const FrDetailPage = () => {
  const { handleProccessFr, handleCloseFieldReport, frDetail, frLogs } = useWo()

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>WO</Typography>
          </Link>
          <Link to='/wo/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>FR Masuk</Typography>
          </Link>
          <Typography>Detil FR</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>No. FR: {frDetail?.number}</Typography>
              <Chip
                label={getStatusConfig(frDetail?.status)?.label}
                color={getStatusConfig(frDetail?.status)?.color as any}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(new Date(), 'dd MMM yyyy HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <Button
              variant='contained'
              disabled={frDetail?.status !== FieldReportStatus.CREATED}
              color={'error'}
              className='is-full sm:is-auto'
              onClick={handleCloseFieldReport}
            >
              Tutup Field Report
            </Button>
            <Button
              variant='contained'
              // disabled={!isCanDoRma}
              // color={!isCanDoRma ? 'secondary' : 'primary'}
              className='is-full sm:is-auto'
              onClick={handleProccessFr}
            >
              Proses FR
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitDetail unitData={frDetail?.unit} />
          </Grid>
          <Grid item xs={12}>
            <DataUnitDetail />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {frDetail?.status === FieldReportStatus.CLOSED && (
            <Grid item xs={12}>
              <CloseReason reason={frDetail?.closeReason} />
            </Grid>
          )}
          <Grid item xs={12}>
            <LocationCard />
          </Grid>
          <Grid item xs={12}>
            <CardProblem />
          </Grid>
          <Grid item xs={12}>
            <CardSchedule />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={frLogs ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default FrDetailPage
