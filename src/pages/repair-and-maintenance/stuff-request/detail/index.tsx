import { <PERSON>rid, Breadcrumbs, <PERSON>po<PERSON>, <PERSON>, But<PERSON> } from '@mui/material'
import { Link } from 'react-router-dom'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import ItemListCard from '../../wp/stuff-req-detail/components/ItemListCard'
import AdditionalInfoCard from '../../wp/stuff-req-detail/components/AdditionalInfoCard'
import CreatedByCard from '../../wp/stuff-req-detail/components/CreatedByCard'
import { useStuffContext } from '../context/StuffContext'
import { stuffRequestStatus } from '../approval/config/utils'
import ApprovalsCard from '../approval/components/ApprovalsCard'
import { useReadStuffApproval } from '@/api/services/wp/mutation'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'

const StuffRequestDetail = () => {
  const { stuffData, router, isTake, isReturn, refetchStuffData } = useStuffContext()
  const { userProfile } = useAuth()

  const { mutate: readMutate } = useReadStuffApproval()

  const ownApproval = stuffData?.approvals?.find(approval => approval.userId === userProfile?.id)

  useEffect(() => {
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          isRead: true,
          stuffId: stuffData?.id,
          approvalId: ownApproval.id
        },
        { onSuccess: () => refetchStuffData() }
      )
    }
  }, [stuffData])

  const actionButton = () => {
    return (
      <>
        {/* <Button
              className='is-full sm:is-auto'
              startIcon={<i className='ri-upload-2-line' />}
              color='secondary'
              variant='outlined'
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button> */}
        {/* <Button
          variant='contained'
          className='is-full sm:is-auto'
          color='primary'
          disabled={stuffData?.status !== 'APPROVED'}
        >
          Kembalikan Barang
        </Button> */}
      </>
    )
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Persetujuan</Typography>
          </Link>
          <Link to={`/stuff-request/${isTake ? 'approvals-take' : 'approvals-return'}`} replace>
            <Typography color='var(--mui-palette-text-disabled)'>
              {isTake ? 'Pengambilan' : 'Pengembalian'} MRO
            </Typography>
          </Link>
          <Typography>Detil Dokumen</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <Typography className='max-sm:text-center max-sm:mt-2 flex items-center gap-2'>
              <Typography variant='h4'>No. Dokumen: {stuffData?.number}</Typography>
              <Chip
                label={stuffRequestStatus(stuffData?.status).label}
                color={stuffRequestStatus(stuffData?.status).color as any}
                variant='tonal'
                size='small'
              />
            </Typography>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(stuffData?.createdAt ?? Date.now(), 'dd MMM yyyy HH:mm', { locale: id })}
              </Typography>
            </div>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>{actionButton()}</div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <ItemListCard data={stuffData} />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DocNumberCard docType='WO' warehouseDoc={stuffData?.workOrder} />
          </Grid>
          <Grid item xs={12}>
            <AdditionalInfoCard data={stuffData} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ApprovalsCard type={isTake ? 'TAKE' : 'RETURN'} />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard data={stuffData} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default StuffRequestDetail
