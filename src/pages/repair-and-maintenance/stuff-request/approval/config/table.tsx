import { StuffRequestType } from '@/types/wpTypes'
import { Chip, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { stuffRequestStatus } from './utils'

const columnHelper = createColumnHelper<StuffRequestType>()

type RowActionType = {
  detail: (item: StuffRequestType) => void
}

export const tablecolumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. Dokumen',
    cell: ({ row }) => (
      <Typography
        color='primary'
        sx={{ cursor: 'pointer' }}
        role='button'
        onClick={() => rowAction.detail(row.original)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={stuffRequestStatus(row.original.status).label}
        size='small'
        variant='tonal'
        color={stuffRequestStatus(row.original.status).color as any}
      />
    )
  }),
  columnHelper.accessor('itemsCount', {
    header: 'Item',
    cell: ({ row }) => <>{row.original.itemsCount} Item</>
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen'
  }),
  columnHelper.accessor('createdByUser.fullName', {
    header: 'Dibuat Oleh',
    cell: ({ row }) => (
      <div className='flex flex-col gap-1'>
        <Typography className='font-semibold'>{row.original.createdByUser.fullName}</Typography>
        <small>{row.original.createdByUser.title}</small>
      </div>
    )
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'dd MMM yyyy', { locale: id })
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <>
          <IconButton onClick={() => rowAction.detail(row.original)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </>
      )
    }
  })
]
