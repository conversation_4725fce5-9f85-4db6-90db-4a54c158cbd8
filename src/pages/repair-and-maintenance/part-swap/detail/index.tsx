import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { usePartSwap } from '../context/PartSwapContext'
import { getStatusConfig } from '../list/config/util'
import UnitSwapCard from './component/UnitSwapCard'
import ItemListCard from './component/ItemListCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import ApprovalsCard from './component/ApprovalsCard'
import ActivityLogCard from './component/ActivityLogCard'
import { pdf } from '@react-pdf/renderer'
import PartSwapPdfDocument from './component/PartSwapPdfDocument'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_QUERY_KEY } from '@/api/services/company/query'

const PartSwapDetail = () => {
  const { partSwapDetail, partSwapLogs } = usePartSwap()

  const { data: originUnit } = useQuery({
    enabled: !!partSwapDetail?.originUnitId,
    queryKey: [UNIT_QUERY_KEY, partSwapDetail?.originUnitId],
    queryFn: () => CompanyQueryMethods.getUnit(partSwapDetail?.originUnitId)
  })

  const { data: destinationUnit } = useQuery({
    enabled: !!partSwapDetail?.destinationUnitId,
    queryKey: [UNIT_QUERY_KEY, partSwapDetail?.destinationUnitId],
    queryFn: () => CompanyQueryMethods.getUnit(partSwapDetail?.destinationUnitId)
  })

  const handlePrint = async () => {
    if (!!partSwapDetail && !!originUnit && !!destinationUnit) {
      const blob = await pdf(
        <PartSwapPdfDocument partswap={partSwapDetail} originUnit={originUnit} destinationUnit={destinationUnit} />
      ).toBlob()
      const url = URL.createObjectURL(blob)
      const printWindow = window.open(url, '_blank')
      printWindow.onload = () => {
        printWindow.print()
        printWindow.onafterprint = () => {
          printWindow.close()
        }
      }
    }
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Part Swap</Typography>
          </Link>
          <Link to='/part-swap/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Request Terbuat</Typography>
          </Link>
          <Typography>Detil Part Swap</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>{partSwapDetail?.number}</Typography>
              <Chip
                label={getStatusConfig(partSwapDetail?.status).label}
                color={getStatusConfig(partSwapDetail?.status).color as any}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {partSwapDetail?.createdAt
                ? formatDate(new Date(partSwapDetail.createdAt), 'dd MMM yyyy HH:mm', {
                    locale: id
                  })
                : '-'}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                color='secondary'
              >
                Ekspor
              </Button>
              */}
              <Button
                color='secondary'
                variant='outlined'
                onClick={handlePrint}
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button>
            </div>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <UnitSwapCard />
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <AdditionalInfoCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ApprovalsCard />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={partSwapLogs ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default PartSwapDetail
