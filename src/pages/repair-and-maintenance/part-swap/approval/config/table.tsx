import { PartSwapType } from '@/types/partSwapTypes'
import { Chip, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { getStatusConfig } from './util'

const columnHelper = createColumnHelper<PartSwapType>()

type RowActionType = {
  detail: (item: PartSwapType) => void
}

export const tableColumns = (rowAction: RowActionType, userId?: string) => [
  columnHelper.accessor('number', {
    header: 'NO DOKUMEN',
    cell: ({ row }) => (
      <Typography
        role='button'
        onClick={() => rowAction.detail(row.original)}
        color='primary'
        sx={{ cursor: 'pointer' }}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => {
      const docStatus = row.original.status
      const ownApproval = row.original.approvals?.find(approval => approval.userId === userId)
      return docStatus !== 'CANCELED' ? (
        <Chip
          label={getStatusConfig(ownApproval?.status)?.label}
          color={getStatusConfig(ownApproval?.status)?.color as any}
          variant='tonal'
          size='small'
        />
      ) : (
        <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
      )
    }
  }),
  columnHelper.accessor('originUnit.number', {
    header: 'KODE UNIT ASAL'
  }),
  columnHelper.accessor('destinationUnit.number', {
    header: 'KODE UNIT TUJUAN'
  }),
  columnHelper.accessor('itemsCount', {
    header: 'PARTS',
    cell: ({ row }) => `${row.original.itemsCount} Part`
  }),
  columnHelper.accessor('site.name', {
    header: 'WORKSHOP'
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) =>
      row.original.createdAt
        ? formatDate(new Date(row.original.createdAt), 'dd MMM yyyy', {
            locale: id
          })
        : '-'
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton
          onClick={() => rowAction.detail(row.original)}
          className='flex gap-2 items-center justify-center cursor-pointer border border-primary rounded-md px-2 py-1 select-none '
        >
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
