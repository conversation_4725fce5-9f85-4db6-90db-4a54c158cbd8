import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { CreateWpDto } from '@/types/wpTypes'
import { Card, CardContent, FormControl, Grid, InputLabel, TextField, Typography } from '@mui/material'
import { toDate } from 'date-fns'
import { Controller, useFormContext } from 'react-hook-form'

const EstimatedCard = () => {
  const { control } = useFormContext<CreateWpDto>()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Estimasi <PERSON>ai</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='estimatedEndedAt'
              rules={{ required: true }}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(date.toISOString())}
                  dateFormat='dd MMM yyyy'
                  minDate={new Date()}
                  customInput={
                    <TextField
                      error={!!error}
                      fullWidth
                      label='Tanggal'
                      placeholder='Pilih Tanggal'
                      className='flex-1'
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='estimatedHourEndedAt'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(date.toISOString())}
                  showTimeSelect
                  showTimeSelectOnly
                  dateFormat='HH:mm'
                  minDate={new Date()}
                  customInput={
                    <TextField
                      fullWidth
                      label='Jam (opsional)'
                      className='flex-1'
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default EstimatedCard
