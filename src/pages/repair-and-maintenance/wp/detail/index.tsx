import { Grid, Breadcrumbs, Typo<PERSON>, Chip, Button } from '@mui/material'
import { Link, useLocation } from 'react-router-dom'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useWp } from '../context/WpContext'
import SegmentCard from './component/SegmentCard'
import UnitDetail from './component/UnitDetail'
import DataUnitDetail from './component/DataUnitDetail'
import ReportWorkingCard from './component/ReportWorkingCard'
import LaborCard from './component/LaborCard'
import EstimatedCard from './component/EstimatedCard'
import ActivityLogCard from './component/ActivityLogCard'
import TimerCard from './component/TimerCard'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_QUERY_KEY } from '@/api/services/company/query'
import { getStatusConfig } from '../created/config/utils'
import LoadingButton from '@mui/lab/LoadingButton'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import TakeReturnCard from './component/TakeReturnCard'
import { pdf } from '@react-pdf/renderer'
import WpPdfDocument from './component/WpPdfDocument'

const WpDetailPage = () => {
  const {
    onWpStop,
    onStartWp,
    rejectWp,
    approveWp,
    wpDetail,
    woData,
    isStartProcessWpLoading,
    isApproveWpLoading,
    isRejectWpLoading
  } = useWp()
  const location = useLocation()

  const isWpIn = location.pathname.includes('wp-in')

  const { data: unitData } = useQuery({
    queryKey: [UNIT_QUERY_KEY, wpDetail?.unit?.id],
    enabled: !!wpDetail?.unit?.id,
    queryFn: async () => {
      const res = await CompanyQueryMethods.getUnit(wpDetail?.unit?.id)
      return res
    }
  })

  const handlePrint = async () => {
    if (wpDetail && woData && unitData) {
      const blob = await pdf(<WpPdfDocument wpData={wpDetail} woData={woData} unit={unitData} />).toBlob()
      const url = URL.createObjectURL(blob)
      const printWindow = window.open(url, '_blank')
      printWindow.onload = () => {
        printWindow.print()
        printWindow.onafterprint = () => {
          printWindow.close()
        }
      }
    }
  }

  const actionButton = () => {
    switch (wpDetail?.status) {
      case 'PENDING':
        return (
          <LoadingButton
            variant='contained'
            loading={isStartProcessWpLoading}
            startIcon={<></>}
            className='is-full sm:is-auto'
            onClick={onStartWp}
          >
            Mulai Pengerjaan
          </LoadingButton>
        )
      case 'IN_PROCESS':
        return (
          <LoadingButton
            variant='contained'
            loading={isStartProcessWpLoading}
            startIcon={<></>}
            className='is-full sm:is-auto'
            onClick={() => onWpStop()}
          >
            Selesaikan Pengerjaan
          </LoadingButton>
        )
      case 'IN_REVIEW':
        return (
          <>
            <LoadingButton
              variant='contained'
              onClick={rejectWp}
              loading={isRejectWpLoading}
              color='error'
              startIcon={<></>}
              className='is-full sm:is-auto'
            >
              Tolak Review
            </LoadingButton>
            <LoadingButton
              variant='contained'
              onClick={approveWp}
              loading={isApproveWpLoading}
              startIcon={<></>}
              className='is-full sm:is-auto'
            >
              Setujui Review
            </LoadingButton>
          </>
        )
      default:
        return <></>
    }
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Process</Typography>
          </Link>
          <Link to={isWpIn ? '/wp-in' : '/wp/list'} replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Process Terbuat</Typography>
          </Link>
          <Typography>Detil Work Process</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <Typography className='max-sm:text-center max-sm:mt-2 flex flex-col md:flex-row items-center gap-2'>
              <Typography variant='h4'>No. WP: {wpDetail?.number}</Typography>
              <Chip
                label={getStatusConfig(wpDetail?.status).label}
                color={getStatusConfig(wpDetail?.status).color as any}
                variant='tonal'
                size='small'
              />
            </Typography>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(wpDetail?.createdAt ?? Date.now(), 'dd MMM yyyy HH:mm', { locale: id })}
              </Typography>
            </div>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <Button
              color='secondary'
              variant='outlined'
              onClick={handlePrint}
              startIcon={<i className='ic-outline-local-printshop' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button>
            {actionButton()}
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <SegmentCard />
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <DocNumberCard docType='WO' warehouseDoc={wpDetail?.workOrder} />
              </Grid>
              <Grid item xs={12}>
                <UnitDetail unitData={unitData} />
              </Grid>
              <Grid item xs={12}>
                <DataUnitDetail unit={unitData} />
              </Grid>
              <Grid item xs={12}>
                <AdditionalInfoCard wpData={wpDetail} />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              {wpDetail?.status !== 'PENDING' && (
                <Grid item xs={12}>
                  <TimerCard />
                </Grid>
              )}
              <Grid item xs={12}>
                <TakeReturnCard />
              </Grid>
              <Grid item xs={12}>
                <ReportWorkingCard />
              </Grid>
              <Grid item xs={12}>
                <LaborCard />
              </Grid>
              <Grid item xs={12}>
                <EstimatedCard />
              </Grid>
              <Grid item xs={12}>
                <ActivityLogCard />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default WpDetailPage
