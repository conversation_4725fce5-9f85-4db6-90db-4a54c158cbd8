import { Grid, Breadcrumbs, Typo<PERSON>, <PERSON>, But<PERSON> } from '@mui/material'
import { Link, useLocation } from 'react-router-dom'
import { useWp } from '../context/WpContext'
import { stuffRequestStatus } from '../detail/config/utils'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import LoadingButton from '@mui/lab/LoadingButton'
import ItemListCard from './components/ItemListCard'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import AdditionalInfoCard from './components/AdditionalInfoCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import CreatedByCard from './components/CreatedByCard'
import DialogAddItemSegmentWo from '@/components/dialogs/add-item-take-return'
import { useEffect, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { StuffReqPayload } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '@/contexts/AuthContext'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useRouter } from '@/routes/hooks'
import Permission from '@/core/components/Permission'
import DialogDetailJournal from '@/components/dialogs/detail-journal-dialog'
import { WO_SEGMENTS_QUERY_KEY } from '@/api/services/rnm/service'
import RnMQueryMethods from '@/api/services/rnm/query'
import { pdf } from '@react-pdf/renderer'
import StuffReqPdfDocument from './components/StuffReqPdfDocument'

const StuffRequestDetail = () => {
  const router = useRouter()
  const { userProfile } = useAuth()
  const { selectedWpId, stuffData, wpDetail, fetchStuffData } = useWp()
  const [openReturnDialog, setOpenReturnDialog] = useState(false)
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null)

  const method = useForm<StuffReqPayload>()
  const scope = DefaultApprovalScope.StuffRequest

  const { reset, getValues } = method

  const { data: segmentData } = useQuery({
    enabled: stuffData?.items?.length > 0 && !!stuffData?.workOrder?.id,
    queryKey: [WO_SEGMENTS_QUERY_KEY, stuffData?.items?.[0]?.workOrderSegmentId, stuffData?.workOrder],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoSegment(stuffData?.workOrder.id, stuffData?.items?.[0]?.workOrderSegmentId)
      return res
    }
  })

  const { data: approverList } = useQuery({
    enabled: !!wpDetail?.siteId && !!wpDetail?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, wpDetail?.siteId, segmentData?.divisionId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: segmentData?.divisionId ?? 'null',
        scope,
        siteId: wpDetail?.siteId,
        departmentId: wpDetail?.departmentId ?? 'null'
      }),
    placeholderData: []
  })

  const handleOpenReturnDialog = () => {
    setOpenReturnDialog(true)
    if (stuffData?.items?.length > 0) {
      reset({
        ...getValues(),
        workProcessId: wpDetail?.id,
        items: stuffData?.items?.map(i => ({ ...i, ...i.item, stuffRequestItemId: i.id, returnQuantity: 0 })) ?? []
      })
    }
  }

  const handlePrint = async () => {
    if (wpDetail && stuffData && segmentData) {
      const blob = await pdf(
        <StuffReqPdfDocument segment={segmentData} wpData={wpDetail} stuffData={stuffData} />
      ).toBlob()
      const url = URL.createObjectURL(blob)
      const printWindow = window.open(url, '_blank')
      printWindow.onload = () => {
        printWindow.print()
        printWindow.onafterprint = () => {
          printWindow.close()
        }
      }
    }
  }

  const actionButton = () => {
    return (
      <>
        <Button
          color='secondary'
          variant='outlined'
          startIcon={<i className='ic-outline-local-printshop' />}
          className='is-full sm:is-auto'
          onClick={handlePrint}
        >
          Cetak
        </Button>
        {/* <Button
              className='is-full sm:is-auto'
              startIcon={<i className='ri-upload-2-line' />}
              color='secondary'
              variant='outlined'
            >
              Ekspor
            </Button>
             */}
        {stuffData?.journalId && (
          <Permission permission={['journal.create']}>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-eye-line' />}
              className='is-full sm:is-auto'
              onClick={() => setSelectedJournalId(stuffData?.journalId)}
            >
              Cek Jurnal
            </Button>
          </Permission>
        )}
        {stuffData?.type === 'TAKE' && (
          <Button
            variant='contained'
            className='is-full sm:is-auto'
            color='primary'
            disabled={stuffData?.status !== 'APPROVED'}
            onClick={handleOpenReturnDialog}
          >
            Kembalikan Barang
          </Button>
        )}
      </>
    )
  }

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Work Process</Typography>
            </Link>
            <Link to='/wp/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Work Process Terbuat</Typography>
            </Link>
            <Link to={`/wp/list/${selectedWpId}`} replace>
              <Typography color='var(--mui-palette-text-disabled)'>Detil Work Process</Typography>
            </Link>
            <Typography>Detil Dokumen</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <Typography className='max-sm:text-center max-sm:mt-2 flex items-center gap-2'>
                <Typography variant='h4'>No. Dokumen: {stuffData?.number}</Typography>
                <Chip
                  label={stuffRequestStatus(stuffData?.status).label}
                  color={stuffRequestStatus(stuffData?.status).color as any}
                  variant='tonal'
                  size='small'
                />
              </Typography>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography className='max-sm:text-center max-sm:mt-2'>
                  {formatDate(stuffData?.createdAt ?? Date.now(), 'dd MMM yyyy HH:mm', { locale: id })}
                </Typography>
              </div>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>{actionButton()}</div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ItemListCard data={stuffData} />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DocNumberCard docType='WO' warehouseDoc={stuffData?.workOrder} />
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard data={stuffData} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <ApprovalDetailCard approvalList={stuffData?.approvals} />
            </Grid>
            <Grid item xs={12}>
              <CreatedByCard data={stuffData} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {!!selectedJournalId && (
        <DialogDetailJournal
          open={!!selectedJournalId}
          setOpen={open => setSelectedJournalId(!open && null)}
          journalId={selectedJournalId}
        />
      )}
      {openReturnDialog && (
        <DialogAddItemSegmentWo
          type='RETURN'
          approverList={approverList?.map(approver => approver.user) ?? []}
          open={openReturnDialog}
          setOpen={open => setOpenReturnDialog(open)}
          onOpenDialogItem={() => {}}
          successCb={() => {
            fetchStuffData()
            router.push(`/wp/list/${wpDetail?.id}`)
          }}
        />
      )}
    </FormProvider>
  )
}

export default StuffRequestDetail
