import { isNullOrUndefined } from '@/utils/helper'
import { z } from 'zod'

export const createFrSchemaDto = z.object({
  destinationSiteId: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  projectId: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  siteId: z.string().uuid().optional().nullable(),
  unitId: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  initialReport: z.string({ message: 'Wajib diisi' }),
  locationPit: z.number().min(1, { message: 'Wajib diisi' }),
  locationNote: z.string().optional().nullable(),
  symptom: z.string().optional().nullable(),
  cause: z.string().optional().nullable(),
  scheduleDate: z.string().optional().nullable(),
  scheduleReminderType: z.enum(['ONE_WEEK_BEFORE', 'ONE_DAY_BEFORE']).optional().nullable(),
  unitKm: z.number().nonnegative().optional().nullable(),
  unitHm: z.number().nonnegative().optional().nullable(),
  unitMnt: z.number().nonnegative().optional().nullable(),
  priority: z.number({ message: 'Wajib diisi' }),
  breakdownDate: z.string({ message: 'Wajib diisi' }),
  breakdownTime: z.string({ message: 'Wajib diisi' })
})

export type FrDtoType = z.infer<typeof createFrSchemaDto>
