import { useMemo } from 'react'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'
import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { useAuth } from '@/contexts/AuthContext'
import { mrPriorityOptions } from '@/pages/repair-and-maintenance/wo/create-wo-mr/config/util'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { formatISO, toDate } from 'date-fns'
import TimePicker from 'rc-time-picker'
import moment from 'moment'

const CardProblem = () => {
  const { userProfile } = useAuth()
  const { control } = useFormContext<FrDtoType>()

  const items = useMemo(() => userProfile?.sites?.filter(site => site.type === 'WORKSHOP') ?? [], [userProfile])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Masalah</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='priority'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='priority-select'>Prioritas</InputLabel>
                  <Select
                    fullWidth
                    id='select-priority'
                    value={String(value)}
                    onChange={e => onChange(+e.target.value)}
                    label='Prioritas'
                    size='medium'
                    labelId='priority-select'
                    inputProps={{ placeholder: 'Prioritas' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {mrPriorityOptions?.map(priority => (
                      <MenuItem key={priority.value} value={priority.value}>
                        <div className='flex items-center gap-2'>
                          <div className={`size-2 ${priority.color}`} />
                          {priority.label}
                        </div>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='breakdownDate'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='dd/MM/yyyy'
                  maxDate={new Date()}
                  customInput={
                    <TextField fullWidth label='Tanggal Breakdown' placeholder='Pilih tanggal' error={!!error} />
                  }
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='breakdownTime'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <div className={`flex flex-col ${error ? 'rc-time-picker-input error' : ''}`}>
                  <TimePicker
                    showSecond={false}
                    minuteStep={1}
                    className='w-full'
                    popupClassName='rc-time-picker-popup'
                    value={value ? moment(value) : null}
                    onChange={(time: any) => {
                      if (time) {
                        const date = new Date()
                        date.setHours(time.hour())
                        date.setMinutes(time.minute())
                        onChange(date.toISOString())
                      } else {
                        onChange(null)
                      }
                    }}
                    placeholder='Jam Breakdown'
                    format='HH:mm'
                    use12Hours={false}
                    inputReadOnly
                  />
                  {error && (
                    <Typography variant='caption' className='text-red-500 mt-1'>
                      {error.message}
                    </Typography>
                  )}
                </div>
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Controller
              control={control}
              name='initialReport'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} label='Laporan Awal' fullWidth multiline rows={3} error={!!error} />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='symptom'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} label='Simptom' fullWidth multiline rows={3} error={!!error} />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='cause'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} label='Penyebab' fullWidth multiline rows={3} error={!!error} />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default CardProblem
