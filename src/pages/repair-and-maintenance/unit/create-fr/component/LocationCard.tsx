import { useMemo } from 'react'
import {
  Card,
  CardContent,
  Grid,
  Typography,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  ListSubheader,
  ListItemText,
  TextField
} from '@mui/material'
import { useAuth } from '@/contexts/AuthContext'
import { useForm<PERSON>ontext, Controller } from 'react-hook-form'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'
import NumberField from '@/components/numeric/NumberField'
import { isNullOrUndefined } from '@/utils/helper'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectStatus, ProjectType } from '@/types/projectTypes'
import { useQuery } from '@tanstack/react-query'

const LocationCard = () => {
  const { userProfile, groupedSiteList } = useAuth()
  const { control } = useFormContext<FrDtoType>()

  const workshops = useMemo(() => userProfile?.sites?.filter(site => site.type === 'WORKSHOP') ?? [], [userProfile])

  const { data: projectListResponse } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER, status: ProjectStatus.ACTIVE }),
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Lokasi</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12} md={8}>
            <Controller
              control={control}
              name='projectId'
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl error={!!error} fullWidth>
                  <InputLabel id='site-select'>Proyek</InputLabel>
                  <Select
                    value={value}
                    onChange={onChange}
                    labelId='site-select'
                    id='site-select'
                    label='Proyek'
                    placeholder='Pilih Proyek'
                    error={!!error}
                  >
                    {projectListResponse?.items?.map(project => (
                      <MenuItem key={project.id} value={project.id}>
                        {project.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='locationPit'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  {...field}
                  {...(!!error && { error: true })}
                  label='Pit'
                  InputProps={{
                    inputComponent: NumberField as any,
                    inputProps: {
                      isAllowed: ({ floatValue }) => floatValue <= 999 || !floatValue
                    }
                  }}
                />
              )}
            />
          </Grid>

          {/* <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='siteId'
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='role-select'>Lokasi Unit</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange(e.target.value)}
                    label='Lokasi Unit'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Lokasi Unit' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {groupedSiteList.map(group => {
                      let children = []
                      children.push(
                        <ListSubheader
                          className='bg-green-50 text-primary font-semibold'
                          key={group.projectId ?? 'no_project'}
                        >
                          {group.project?.name || 'Tanpa Proyek'}
                        </ListSubheader>
                      )
                      group.sites.forEach(site => {
                        children.push(
                          <MenuItem key={site.id} value={site.id}>
                            <ListItemText primary={site.name} />
                          </MenuItem>
                        )
                      })
                      return children
                    })}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='locationPit'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  {...field}
                  {...(!!error && { error: true })}
                  label='Pit'
                  InputProps={{
                    inputComponent: NumberField as any,
                    inputProps: {
                      isAllowed: ({ floatValue }) => floatValue <= 999 || !floatValue
                    }
                  }}
                />
              )}
            />
          </Grid> */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='locationNote'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  {...field}
                  label='Detil Lokasi Proyek (Opsional)'
                  placeholder='Detil Lokasi (Opsional)'
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Controller
              control={control}
              name='destinationSiteId'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='role-select'>Dikerjakan oleh Workshop</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange(e.target.value)}
                    label='Dikerjakan oleh Workshop'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Dikerjakan oleh Workshop' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {workshops?.map(site => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default LocationCard
