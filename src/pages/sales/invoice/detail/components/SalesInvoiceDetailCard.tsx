// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

// Context
import { useAuth } from '@/contexts/AuthContext'
import { toCurrency } from '@/utils/helper'
import { useSalesInvoice } from '../../context/SalesInvoiceContext'

const SalesInvoiceDetailCard = () => {
  const { allSites, departmentList } = useAuth()
  const { salesInvoiceData } = useSalesInvoice()

  if (!salesInvoiceData) return null

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Detil Faktur</Typography>

        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Customer
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {salesInvoiceData.customer?.name || '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Tanggal Faktur
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {formatDate(salesInvoiceData.invoiceDate, 'dd/MM/yyyy', { locale: id })}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Metode Pembayaran
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {salesInvoiceData.paymentTerms || '-'} {salesInvoiceData?.paymentDueDays || ''}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Jatuh Tempo
            </Typography>
            <Typography variant='body1' className='font-medium text-red-500'>
              {salesInvoiceData.paymentDueDate
                ? formatDate(salesInvoiceData.paymentDueDate, 'dd/MM/yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Nomor Faktur Pajak
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {salesInvoiceData.taxInvoiceNumber ?? '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Tanggal Faktur Pajak
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {salesInvoiceData.taxInvoiceDate
                ? formatDate(salesInvoiceData.taxInvoiceDate, 'dd/MM/yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Mata Uang
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {salesInvoiceData.currency?.name ?? 'Rupiah'}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Konversi ke Rupiah
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {toCurrency(salesInvoiceData.exchangeRate ?? 1)}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Dibuat Oleh
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {salesInvoiceData.createdByUser?.fullName || '-'} ({salesInvoiceData.createdByUser?.title})
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Memo
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {salesInvoiceData.note || '-'}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SalesInvoiceDetailCard
