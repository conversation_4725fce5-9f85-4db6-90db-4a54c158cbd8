// MUI Imports
import Grid from '@mui/material/Grid'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Chip, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useSalesInvoice } from '../context/SalesInvoiceContext'

// Components
// import PurchaseOrderCard from './components/PurchaseOrderCard'
import DialogDetailJournal from '@/components/dialogs/detail-journal-dialog'
import { useReadSalesInvoice } from '@/api/services/sales-invoice/mutation'

import { saveAs } from 'file-saver'
import { pdf } from '@react-pdf/renderer'
import InvoicePdfDocument from '../detail/components/InvoicePdfDocument'
import PaymentSummaryCard from '../detail/components/PaymentSummaryCard'
import OtherExpenseCard from '../detail/components/OtherExpenseCard'
import ItemsCard from '../detail/components/ItemsCard'
import DocumentCard from '../detail/components/DocumentCard'
import SalesInvoiceDetailPageWithProvider from '../detail'
import ApprovalsCard from './components/ApprovalsCard'
import SalesInvoiceDetailCard from '../detail/components/SalesInvoiceDetailCard'
import { format, formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import Permission from '@/core/components/Permission'
import { statusChipValue } from '../approval/config/table'
import { SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'

const SalesInvoiceApprovalDetailPage = () => {
  const { userProfile } = useAuth()
  const { salesInvoiceData, fetchSalesInvoiceList } = useSalesInvoice()
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null)

  const { mutate: readMutate } = useReadSalesInvoice()

  const ownApproval = salesInvoiceData?.approvals?.find(approval => approval.userId === userProfile?.id)

  const handleExport = async () => {
    const blob = await pdf(<InvoicePdfDocument salesInvoice={salesInvoiceData} />).toBlob()
    const filename = salesInvoiceData?.number + '.pdf'
    saveAs(blob, filename)
  }

  const handlePrint = async isPreview => {
    const blob = await pdf(
      <InvoicePdfDocument
        salesInvoice={salesInvoiceData}
        isPreview={isPreview}
        // qrCode={qr}
      />
    ).toBlob()
    const url = URL.createObjectURL(blob)
    const printWindow = window.open(url, '_blank')
    printWindow.onload = () => {
      printWindow.print()
      printWindow.onafterprint = () => {
        printWindow.close()
      }
    }
  }

  useEffect(() => {
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          id: salesInvoiceData?.id,
          approvalId: ownApproval.id,
          payload: {
            isRead: true
          }
        },
        { onSuccess: () => fetchSalesInvoiceList() }
      )
    }
  }, [salesInvoiceData])

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Persetujuan</Typography>
            </Link>
            <Link to='/sales/invoice/approval' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Faktur Penjualan</Typography>
            </Link>
            <Typography>Detil Faktur</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>No. Faktur: {salesInvoiceData?.number}</Typography>
                {ownApproval && (
                  <Chip
                    label={statusChipValue[ownApproval?.status]?.label}
                    color={statusChipValue[ownApproval?.status]?.color}
                    variant='tonal'
                    size='small'
                  />
                )}{' '}
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {format(salesInvoiceData?.createdAt ?? Date.now(), 'dd MMM yyyy', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 is-full sm:is-auto'>
              {/* <Button color='error' onClick={() => router.back()} variant='outlined'>
                Hapus Faktur
              </Button> */}
              {salesInvoiceData?.status === SalesInvoiceStatuses.APPROVED ? (
                <>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-upload-2-line' />}
                    className='is-full sm:is-auto'
                    onClick={handleExport}
                  >
                    Ekspor
                  </Button>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-printer-line' />}
                    className='is-full sm:is-auto'
                    onClick={handlePrint}
                  >
                    Cetak
                  </Button>
                </>
              ) : (
                <Button
                  color='secondary'
                  variant='outlined'
                  startIcon={<i className='ri-printer-line' />}
                  className='is-full sm:is-auto'
                  onClick={() => handlePrint(true)}
                >
                  Preview Cetak
                </Button>
              )}
              {salesInvoiceData?.journalId && (
                <Permission permission={['journal.create']}>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-eye-line' />}
                    className='is-full sm:is-auto'
                    onClick={() => setSelectedJournalId(salesInvoiceData?.journalId)}
                  >
                    Cek Jurnal
                  </Button>
                </Permission>
              )}
            </div>
          </div>
        </Grid>
        {/* Left Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Sales Invoice Detail Card */}
            <Grid item xs={12}>
              <SalesInvoiceDetailCard />
            </Grid>
          </Grid>
        </Grid>
        {/* Right Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Document Card */}
            <Grid item xs={12}>
              <DocumentCard />
            </Grid>

            {/* Approvals Card */}
            {(salesInvoiceData?.approvals?.length ?? 0) > 0 && (
              <Grid item xs={12}>
                <ApprovalsCard />
              </Grid>
            )}
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container spacing={4}>
            {/* Items Card */}
            <Grid item xs={12}>
              <ItemsCard />
            </Grid>

            {/* Other Expense Card */}
            <Grid item xs={12}>
              <OtherExpenseCard />
            </Grid>

            {/* Payment Summary Card */}
            <Grid item xs={12}>
              <PaymentSummaryCard />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {!!selectedJournalId && (
        <DialogDetailJournal
          open={!!selectedJournalId}
          setOpen={open => setSelectedJournalId(!open && null)}
          journalId={selectedJournalId}
        />
      )}
    </>
  )
}

export default SalesInvoiceApprovalDetailPage
