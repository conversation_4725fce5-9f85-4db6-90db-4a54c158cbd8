import { createColumnHelper } from '@tanstack/react-table'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { CashReceiptType } from '@/pages/cash-bank/receipt/config/types'
import { id } from 'date-fns/locale'
import { getStatusConfig } from '@/pages/cash-bank/payments/config/utils'
import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'

const columnHelper = createColumnHelper<CashReceiptType>()

type RowAction = {
  onDetail: (row: CashReceiptType) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'No. Penerimaan',
    cell: ({ row }) => (
      <Typography onClick={() => rowAction.onDetail(row.original)} color='primary'>
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('salesInvoice.number', {
    header: 'No. Faktur'
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={getStatusConfig(row.original.status).label}
        color={getStatusConfig(row.original.status).color as any}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Faktur',
    cell: ({ row }) => (
      <Typography color='primary' className='font-semibold'>
        {formatThousandSeparator(row.original?.totalAmount ?? 0, true)}
      </Typography>
    )
  }),
  columnHelper.accessor('voucherNumber', {
    header: 'No Voucher'
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => {
      return <Typography>{formatDate(row.original.createdAt, 'dd MMM yyyy', { locale: id })}</Typography>
    }
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Aksi',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.onDetail(row.original)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
