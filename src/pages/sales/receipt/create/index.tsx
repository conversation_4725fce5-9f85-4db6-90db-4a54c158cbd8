import { Grid, Typo<PERSON>, Breadcrum<PERSON>, But<PERSON> } from '@mui/material'
import { Link, useSearchParams } from 'react-router-dom'
import { useRouter } from '@/routes/hooks'
import { FormProvider, useForm, useWatch } from 'react-hook-form'
import ReceiptDetailCard from './components/PaymentDetailCard'
import ApprovalListCard from './components/ApprovalListCard'
import InvoiceCard from './components/InvoiceCard'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { CreateCashReceiptPayload } from '@/pages/cash-bank/receipt/config/types'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { useCreateCashReceipt } from '@/api/services/cashbank/mutation'
import { toast } from 'react-toastify'
import { useSalesR<PERSON>eipt } from '../context/SalesReceiptContext'
import DocInvoiceCard from '@/pages/cash-bank/receipt/detail/components/DocInvoiceCard'
import InvoiceDetailCard from '@/pages/cash-bank/receipt/detail/components/InvoiceDetailCard'
import SalesInvoiceQueryMethods, { SALES_INVOICE_QUERY_KEY } from '@/api/services/sales-invoice/query'

const CreateSalesReceipt = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const [searchParams] = useSearchParams()

  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string>()

  const methods = useForm<CreateCashReceiptPayload>()
  const { handleSubmit, reset, getValues, control, setValue } = methods

  const { mutate: createMutate, isLoading: loadingMutate } = useCreateCashReceipt()

  const { data: salesInvoiceData } = useQuery({
    enabled: !!selectedInvoiceId,
    queryKey: [SALES_INVOICE_QUERY_KEY, selectedInvoiceId],
    queryFn: () => SalesInvoiceQueryMethods.getSalesInvoice(selectedInvoiceId)
  })

  const scope = `cash-receipt`

  const { data: approverList } = useQuery({
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope,
        siteId: 'null',
        departmentId: 'null'
      }),
    placeholderData: []
  })

  const onSubmit = (data: CreateCashReceiptPayload) => {
    setConfirmState({
      open: true,
      title: 'Buat Penerimaan Penjualan',
      content:
        'Apakah kamu yakin akan membuat penerimaan penjualan untuk faktur ini? Pastikan semua data sudah benar, action ini tidak bisa diubah',
      confirmText: 'Buat Penerimaan Penjualan',
      onConfirm: () => {
        createMutate(
          { ...data, approvals: approverList.map(approver => ({ userId: approver.user?.id })) },
          {
            onSuccess: () => {
              toast.success('Pencatatan berhasil dibuat')
              router.push('/cash-bank/receipt/list', { replace: true })
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (searchParams.get('invoiceId')) {
      reset({
        ...getValues(),
        type: 'SALES',
        salesInvoiceId: searchParams.get('invoiceId')
      })
      setSelectedInvoiceId(searchParams.get('invoiceId'))
    }
  }, [searchParams])

  return (
    <FormProvider {...methods}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Typography>Buat Penerimaan</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex flex-col md:flex-row justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Tambah Penerimaan</Typography>
              <Typography>Lengkapi data dan tambahkan pencatatan Penerimaan</Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button
                // disabled={loadingMutate}
                onClick={() => router.back()}
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
              >
                Batalkan
              </Button>
              <Button
                variant='contained'
                // disabled={loadingMutate}
                onClick={handleSubmit(onSubmit)}
                className='is-full sm:is-auto'
              >
                Buat Penerimaan
              </Button>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <InvoiceCard invoiceData={salesInvoiceData} />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <ReceiptDetailCard invoiceData={salesInvoiceData} />
            </Grid>
            <Grid item xs={12}>
              <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
            </Grid>
          </Grid>
        </Grid>
        {salesInvoiceData && (
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <InvoiceDetailCard purchaseInvoiceId={salesInvoiceData?.id} />
              </Grid>
            </Grid>
          </Grid>
        )}
      </Grid>
    </FormProvider>
  )
}

export default CreateSalesReceipt
