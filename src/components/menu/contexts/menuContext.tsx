// React Imports
import { createContext, useCallback, useContext, useMemo, useState } from 'react'

// Type Imports
import type { ChildrenType } from '../types'
import ConfirmDialog from '@/components/dialogs/confirm-dialog'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useAuth } from '@/contexts/AuthContext'
import dictionary from '@/data/dictionaries/id.json'
import { MenuDataType } from '@/types/menuTypes'
import { permissionMap } from '../permissionMappings'

export type MenuState = {
  width?: number
  collapsedWidth?: number
  isCollapsed?: boolean
  isHovered?: boolean
  isToggled?: boolean
  isScrollWithContent?: boolean
  isBreakpointReached?: boolean
  isPopoutWhenCollapsed?: boolean
  collapsing?: boolean // for internal use only
  expanding?: boolean // for internal use only
  transitionDuration?: number
}

export type ConfirmDialogType = {
  open: boolean
  title: string
  content: string
  cancelText?: string
  confirmText: string
  confirmColor?: string
  onConfirm: () => void
  onCancel?: () => void
}

export type MenuContextProps = MenuState & {
  updateVerticalNavState: (values: MenuState) => void
  collapseVerticalNav: (value?: MenuState['isCollapsed']) => void
  hoverVerticalNav: (value?: MenuState['isHovered']) => void
  toggleVerticalNav: (value?: MenuState['isToggled']) => void
  confirmState: ConfirmDialogType
  setConfirmState: React.Dispatch<React.SetStateAction<ConfirmDialogType>>
  menuData: MenuDataType[]
  hasPermissionForUrl: (url: string) => boolean
}

const MenuContext = createContext({} as MenuContextProps)

export const useMenu = () => {
  return useContext(MenuContext)
}

export const MenuProvider = ({ children }: ChildrenType) => {
  const { accountPermissions, offlinePermissions, approvalCounts } = useAuth()
  const {
    mrCount,
    prCount,
    mtCount,
    mbCount,
    mgOutCount,
    smCount,
    rmaCount,
    srCount,
    preReleaseCount,
    sreqCount,
    pwCount,
    poCount,
    purchaseInvoiceCount,
    soCount,
    wpTakeCount,
    wpReturnCount,
    paymentCount,
    cashReceiptCount,
    salesInvoiceCount
  } = approvalCounts
  const [confirmState, setConfirmState] = useState<ConfirmDialogType>({
    open: false,
    title: '',
    content: '',
    confirmText: '',
    confirmColor: '',
    onConfirm: () => {},
    onCancel: () => {}
  })
  // States
  const [verticalNavState, setVerticalNavState] = useState<MenuState>()

  // Hooks
  const updateVerticalNavState = useCallback((values: Partial<MenuState>) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      ...values,
      collapsing: values.isCollapsed === true,
      expanding: values.isCollapsed === false
    }))
  }, [])

  const collapseVerticalNav = useCallback((value?: boolean) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      isHovered: value !== undefined && false,
      isCollapsed: value !== undefined ? Boolean(value) : !Boolean(prevState?.isCollapsed),
      collapsing: value === true,
      expanding: value !== true
    }))
  }, [])

  const hoverVerticalNav = useCallback((value?: boolean) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      isHovered: value !== undefined ? Boolean(value) : !Boolean(prevState?.isHovered)
    }))
  }, [])

  const toggleVerticalNav = useCallback((value?: boolean) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      isToggled: value !== undefined ? Boolean(value) : !Boolean(prevState?.isToggled)
    }))
  }, [])

  const combinedPermissions = useMemo(() => {
    const allPermissions = new Set<string>()

    accountPermissions?.forEach(p => allPermissions.add(p))
    offlinePermissions?.forEach(p => allPermissions.add(p))

    return allPermissions
  }, [accountPermissions, offlinePermissions])

  const hasPermission = useCallback(
    (requiredPermissions: string[], isParent?: boolean): boolean => {
      // If no permissions are required, access is granted.
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return true
      }

      // Create a Set of required permissions for efficient lookup.
      const requiredPermissionSet = new Set(requiredPermissions)

      // Check if any of the user's permissions match the required ones.
      for (const userPermission of combinedPermissions) {
        const permissionToCheck = isParent ? userPermission.split('.')[0] ?? '' : userPermission

        if (requiredPermissionSet.has(permissionToCheck)) {
          return true // Permission found
        }
      }

      return false // No matching permission found
    },
    [combinedPermissions]
  )

  const getFilteredMenu = (permissions: string[], menu: MenuDataType, isParent?: boolean) => {
    return hasPermission(permissions, isParent) ? [menu] : []
  }

  const menuData =
    combinedPermissions.size > 0
      ? [
          {
            id: 'approvals-section',
            isSection: true,
            label: 'Menu Persetujuan',
            children: [
              ...getFilteredMenu(permissionMap['/user/default-approval'], {
                id: 'default-approval',
                label: dictionary['navigation'].defaultApproval,
                icon: 'ri-user-line',
                href: `/user/default-approval`
              }),
              {
                id: 'approvals-section',
                label: dictionary['navigation'].persetujuan,
                suffix:
                  mrCount > 0 ||
                  prCount > 0 ||
                  mtCount > 0 ||
                  mbCount > 0 ||
                  mgOutCount > 0 ||
                  smCount > 0 ||
                  srCount > 0 ||
                  rmaCount > 0 ||
                  wpTakeCount > 0 ||
                  paymentCount > 0 ||
                  cashReceiptCount > 0 ||
                  wpReturnCount > 0 ? (
                    <i className='red-dot-icon size-2 text-error' />
                  ) : null,
                icon: 'approvals-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/mr/approval'], {
                    id: 'mr-approval',
                    label: dictionary['navigation'].altMaterialRequest,
                    href: `/mr/approval`,
                    suffix:
                      mrCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {mrCount > 9 ? '9+' : mrCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/pr/approval'], {
                    id: 'pr-approval',
                    label: dictionary['navigation'].altpersetujuanPp,
                    href: `/pr/approval`,
                    suffix:
                      prCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {prCount > 9 ? '9+' : prCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/mt/approval'], {
                    id: 'mt-approval',
                    label: dictionary['navigation'].materialTransfer,
                    href: '/mt/approval',
                    suffix:
                      mtCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {mtCount > 9 ? '9+' : mtCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/mb/approval'], {
                    id: 'mb-approval',
                    label: dictionary['navigation'].materialBorrow,
                    href: '/mb/approval',
                    suffix:
                      mbCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {mbCount > 9 ? '9+' : mbCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/mg/out/approval'], {
                    id: 'req-out',
                    label: dictionary['navigation'].barangKeluar,
                    href: '/mg/out/approval',
                    suffix:
                      mgOutCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {mgOutCount > 9 ? '9+' : mgOutCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/mg/sm/approval'], {
                    id: 'approve-stock-movement',
                    label: dictionary['navigation'].pindahBarang,
                    href: '/mg/sm/approval',
                    suffix:
                      smCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {smCount > 9 ? '9+' : smCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/sr/approval'], {
                    id: 'stock-approval',
                    label: 'Pengembalian Stok',
                    href: '/sr/approval',
                    suffix:
                      srCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {srCount > 9 ? '9+' : srCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/po/approval'], {
                    id: 'po-approval',
                    label: 'Purchase Order',
                    href: `/po/approval`,
                    suffix:
                      poCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {poCount > 9 ? '9+' : poCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/purchase-invoice/approval'], {
                    id: 'purchaseinvoice-approval',
                    label: 'Faktur Pembelian',
                    href: `/purchase-invoice/approval`,
                    suffix:
                      purchaseInvoiceCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {purchaseInvoiceCount > 9 ? '9+' : purchaseInvoiceCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/sales-invoice/approval'], {
                    id: 'salesinvoice-approval',
                    label: 'Faktur Penjualan',
                    href: `/sales/invoice/approval`,
                    suffix:
                      salesInvoiceCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {salesInvoiceCount > 9 ? '9+' : salesInvoiceCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/service-order/approval'], {
                    id: 'so-approval',
                    label: 'Service Order',
                    href: `/service-order/approval`,
                    suffix:
                      soCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {soCount > 9 ? '9+' : soCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/rma/approval'], {
                    id: 'rma-approval',
                    label: 'RMA',
                    href: '/rma/approval',
                    suffix:
                      rmaCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {rmaCount > 9 ? '9+' : rmaCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/stuff-request/approvals-take'], {
                    id: 'stuff-request-approval',
                    label: 'Pengambilan Barang WO',
                    href: '/stuff-request/approvals-take',
                    suffix:
                      wpTakeCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {wpTakeCount > 9 ? '9+' : wpTakeCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/stuff-request/approvals-return'], {
                    id: 'stuff-request-approval',
                    label: 'Pengembalian Barang WO',
                    href: '/stuff-request/approvals-return',
                    suffix:
                      wpReturnCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {wpReturnCount > 9 ? '9+' : wpReturnCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/wo/approval-pre-releases'], {
                    id: 'approval-pre-release-list',
                    label: 'Pre-Release',
                    href: '/wo/approval-pre-releases',
                    suffix:
                      preReleaseCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {preReleaseCount > 9 ? '9+' : preReleaseCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/service-request/approval'], {
                    id: 'service-request-approval',
                    label: 'Service Request',
                    href: '/service-request/approval',
                    suffix:
                      sreqCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {sreqCount > 9 ? '9+' : sreqCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/part-swap/approval'], {
                    id: 'part-swap-approval',
                    label: 'Part Swap',
                    href: '/part-swap/approval',
                    suffix:
                      pwCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {pwCount > 9 ? '9+' : pwCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/cash-bank/payment-approval'], {
                    id: 'cash-bank-payment',
                    label: 'Pembayaran',
                    href: '/cash-bank/payment-approval',
                    suffix:
                      paymentCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {paymentCount > 9 ? '9+' : paymentCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(permissionMap['/cash-bank/receipts-approval'], {
                    id: 'cash-receipt-approval',
                    label: 'Penerimaan',
                    href: '/cash-bank/receipts-approval',
                    suffix:
                      cashReceiptCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-[2px] px-[5px] text-xxs'>
                          {cashReceiptCount > 9 ? '9+' : cashReceiptCount}
                        </div>
                      ) : null
                  })
                ]
              }
            ]
          },
          {
            id: 'warehouse-section',
            isSection: true,
            label: dictionary['navigation'].warehouseManagement,
            children: [
              {
                id: 'warehouse',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/warehouse`
              },
              ...getFilteredMenu(
                [
                  ...permissionMap['/company-data/assets/goods'],
                  ...permissionMap['/mg/in'],
                  ...permissionMap['/mg/out/request']
                ],
                {
                  id: 'material-goods',
                  label: 'Material/Barang',
                  icon: 'mdi-layers-triple-outline',
                  children: [
                    ...getFilteredMenu(permissionMap['/company-data/assets/goods'], {
                      id: 'goods',
                      label: 'Barang & Jasa',
                      children: [
                        ...getFilteredMenu(permissionMap['/company-data/assets/goods'], {
                          id: 'item-list',
                          label: 'List Barang & Jasa',
                          href: '/company-data/assets/goods'
                        }),
                        ...getFilteredMenu(permissionMap['/company-data/category/item'], {
                          id: 'category-list-item',
                          label: 'Kategori Barang & Jasa',
                          href: `/company-data/category/item`
                        }),
                        ...getFilteredMenu(permissionMap['/company-data/group-category/item'], {
                          id: 'group-category-list-item',
                          label: 'Kelompok Barang & Jasa',
                          href: `/company-data/group-category/item`
                        })
                      ]
                    }),
                    // ...getFilteredMenu(permissionMap['/company-data/assets/goods'], {
                    //   id: 'service',
                    //   label: 'Jasa',
                    //   children: [
                    //     { id: 'service-list', label: 'List Jasa', href: '/sales/service/list' },
                    //     {
                    //       id: 'service-category',
                    //       label: 'Kategori Jasa',
                    //       href: '/sales/service/category'
                    //     }
                    //   ]
                    // }),
                    ...getFilteredMenu(permissionMap['/mg/track'], {
                      id: 'track-item',
                      label: 'Lacak Barang & Jasa',
                      href: '/mg/track'
                    }),
                    ...getFilteredMenu(permissionMap['/mg/in'], {
                      id: 'mg-in',
                      label: 'Barang & Jasa Masuk',
                      href: '/mg/in'
                    }),
                    ...getFilteredMenu(permissionMap['/mg/out/request'], {
                      id: 'mg-out',
                      label: 'Barang Keluar',
                      href: '/mg/out/request'
                    }),
                    ...getFilteredMenu(permissionMap['/mg/out/draft'], {
                      id: 'mg-out',
                      label: 'Draft Barang Keluar',
                      href: '/mg/out/draft'
                    }),
                    {
                      id: 'stock-movement',
                      label: 'Pindah Barang',
                      children: [
                        ...getFilteredMenu(permissionMap['/mg/sm/list'], {
                          id: 'created-stock-movement',
                          label: 'Terbuat',
                          href: '/mg/sm/list'
                        }),
                        ...getFilteredMenu(permissionMap['/mg/sm/receive'], {
                          id: 'receive-stock-movement',
                          label: 'Penerimaan',
                          href: '/mg/sm/receive'
                        })
                      ]
                    }
                  ]
                }
              ),
              ...getFilteredMenu(
                [DefaultApprovalScope.MaterialRequest],
                {
                  id: 'material-request',
                  label: dictionary['navigation'].materialRequest,
                  icon: 'ic-outline-file-upload',
                  children: [
                    ...getFilteredMenu(permissionMap['/mr/create'], {
                      id: 'mr-create',
                      label: dictionary['navigation'].buatMr,
                      href: `/mr/create`
                    }),
                    ...getFilteredMenu(permissionMap['/mr/create'], {
                      id: 'my-mr-list',
                      label: 'Request Saya',
                      href: `/mr/my-list`
                    }),
                    ...getFilteredMenu(permissionMap['/mr/list'], {
                      id: 'mr-list',
                      label: dictionary['navigation'].mrTerbuat,
                      href: `/mr/list`
                    }),
                    ...getFilteredMenu(permissionMap['/mr/draft'], {
                      id: 'mr-list',
                      label: 'Draft',
                      href: `/mr/draft`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(permissionMap['/mr-in'], {
                id: 'mr-created',
                label: dictionary['navigation'].mrMasuk,
                icon: 'ri-folder-download-line',
                href: '/mr-in'
              }),
              ...getFilteredMenu(
                [
                  'material-transfer.read',
                  'material-transfer.create',
                  'material-transfer.approve',
                  'material-transfer.receive',
                  'material-transfer.delivery'
                ],
                {
                  id: 'mt',
                  label: 'Material Transfer',
                  icon: 'solar-card-transfer-linear',
                  children: [
                    ...getFilteredMenu(permissionMap['/mt/created'], {
                      id: 'mt-created',
                      label: 'Terbuat',
                      href: '/mt/created'
                    }),
                    ...getFilteredMenu(permissionMap['/mt/list'], {
                      id: 'mt-list',
                      label: 'Permintaan Masuk',
                      href: '/mt/list'
                    }),
                    ...getFilteredMenu(permissionMap['/mt/receive'], {
                      id: 'mt-receive',
                      label: 'Penerimaan',
                      href: '/mt/receive'
                    }),
                    ...getFilteredMenu(permissionMap['/mt/draft'], {
                      id: 'mt-draft',
                      label: 'Draft',
                      href: '/mt/draft'
                    })
                  ]
                }
              ),
              ...getFilteredMenu(
                [
                  'stock-return.read',
                  'material-transfer.create',
                  'material-transfer.approve',
                  'material-transfer.receive',
                  'material-transfer.delivery'
                ],
                {
                  id: 'mb',
                  label: 'Material Borrow',
                  icon: 'streamline-arrow-infinite-loop',
                  children: [
                    ...getFilteredMenu(permissionMap['/mb/created'], {
                      id: 'mb-created',
                      label: 'Terbuat',
                      href: '/mb/created'
                    }),
                    ...getFilteredMenu(permissionMap['/mb/list'], {
                      id: 'mb-list',
                      label: dictionary['navigation'].incomingRequest,
                      href: '/mb/list'
                    }),
                    ...getFilteredMenu(permissionMap['/mb/receive'], {
                      id: 'mb-receive',
                      label: 'Penerimaan',
                      href: '/mb/receive'
                    }),
                    ...getFilteredMenu(permissionMap['/mb/return-list'], {
                      id: 'mb-return-list',
                      label: 'Pengembalian',
                      href: '/mb/return-list'
                    })
                  ]
                }
              ),
              {
                id: 'mg-stock',
                label: 'Stok',
                icon: 'stock-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/mg/stock'], {
                    id: 'mg-stock',
                    label: 'Stok Barang',
                    href: '/mg/stock'
                  }),
                  ...getFilteredMenu(permissionMap['/mg/so'], {
                    id: 'mg-stock-opname',
                    label: 'Stok Opnam',
                    href: '/mg/so'
                  }),
                  ...getFilteredMenu(['stock-return.create'], {
                    id: 'stock-return',
                    label: 'Pengembalian Stok',
                    children: [
                      {
                        id: 'stock-return-request',
                        label: 'Pengajuan',
                        href: '/sr/request'
                      },
                      {
                        id: 'stock-return-draft',
                        label: 'Draft',
                        href: '/sr/draft'
                      }
                    ]
                  })
                ]
              }
            ]
          },
          {
            id: 'purchasing-section',
            isSection: true,
            label: dictionary['navigation'].purchasing,
            children: [
              {
                id: 'purchasing',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/purchasing`
              },
              {
                id: 'vendor-section',
                label: 'Vendor',
                icon: 'ri-store-2-line',
                children: [
                  ...getFilteredMenu(permissionMap['/company-data/vendor'], {
                    id: 'vendor-list',
                    label: 'List',
                    href: '/company-data/vendor'
                  }),
                  ...getFilteredMenu(permissionMap['/company-data/category/vendor'], {
                    id: 'category-list-vendor',
                    label: 'Kategori',
                    href: `/company-data/category/vendor`
                  })
                ]
              },
              ...getFilteredMenu(
                [DefaultApprovalScope.PurchaseRequisition],
                {
                  id: 'purchase-requisition',
                  label: dictionary['navigation'].permintaanPembelian,
                  icon: 'mdi-file-document-outline',
                  children: [
                    ...getFilteredMenu(permissionMap['/pr/list'], {
                      id: 'pr-list',
                      label: dictionary['navigation'].ppTerbuat,
                      href: `/pr/list`
                    }),
                    ...getFilteredMenu(permissionMap['/pr/draft'], {
                      id: 'pr-drafts',
                      label: 'Draft',
                      href: `/pr/draft`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(
                [DefaultApprovalScope.PurchaseOrder],
                {
                  id: 'purchase-order',
                  label: dictionary['navigation'].purchaseOrder,
                  icon: 'heroicons-document-arrow-down',
                  children: [
                    ...getFilteredMenu(permissionMap['/po/pr-list'], {
                      id: 'pr-created',
                      label: dictionary['navigation'].incomingRequest,
                      href: `/po/pr-list`
                    }),
                    ...getFilteredMenu(permissionMap['/po/list'], {
                      id: 'po-list',
                      label: dictionary['navigation'].poTerbuat,
                      href: `/po/list`
                    }),
                    ...getFilteredMenu(permissionMap['/po/draft'], {
                      id: 'po-list',
                      label: 'Draft',
                      href: `/po/draft`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(
                [DefaultApprovalScope.ServiceOrder],
                {
                  id: 'service-order',
                  label: 'Service Order',
                  icon: 'heroicons-document-arrow-down',
                  children: [
                    ...getFilteredMenu(permissionMap['/service-order/sr-list'], {
                      id: 'sr-created',
                      label: dictionary['navigation'].incomingRequest,
                      href: `/service-order/sr-list`
                    }),
                    ...getFilteredMenu(permissionMap['/service-order/list'], {
                      id: 'so-list',
                      label: 'Terbuat',
                      href: `/service-order/list`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(
                [DefaultApprovalScope.Rma],
                {
                  id: 'rma',
                  label: 'RMA',
                  icon: 'ic-rma-outline',
                  children: [
                    ...getFilteredMenu(permissionMap['/rma/list'], {
                      id: 'rma-list',
                      label: 'Terbuat',
                      href: '/rma/list'
                    }),
                    // ...getFilteredMenu(['rma.create'], {
                    //   id: 'request-rma',
                    //   label: 'Pengajuan',
                    //   href: '/rma/request'
                    // }),
                    ...getFilteredMenu(permissionMap['/rma/draft'], {
                      id: 'draft-rma',
                      label: 'Draft',
                      href: '/rma/draft'
                    })
                  ]
                },
                true
              )
            ]
          },
          {
            id: 'repair-section',
            isSection: true,
            label: dictionary['navigation'].repairandmaintenance,
            children: [
              {
                id: 'workshop',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/workshop`
              },
              {
                id: 'unit-asset',
                label: 'Manajemen Unit',
                icon: 'asset-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/company-data/assets/unit'], {
                    id: 'unit-asset-list',
                    label: 'Unit',
                    // icon: 'ri-truck-line',
                    children: [
                      {
                        id: 'unit-list',
                        label: 'List',
                        href: '/company-data/assets/unit/list'
                      },
                      {
                        id: 'unit-creation',
                        label: 'Buat',
                        href: '/company-data/assets/unit/-/new'
                      },
                      {
                        id: 'unit-draft-creation',
                        label: 'Draft',
                        href: '/company-data/assets/unit/draft'
                      }
                    ]
                  }),
                  ...getFilteredMenu(permissionMap['/company-data/assets/document'], {
                    id: 'document-asset',
                    label: dictionary['navigation'].documentAsset,
                    href: '/company-data/assets/document'
                  }),
                  ...getFilteredMenu(permissionMap['/company-data/assets/insurance'], {
                    id: 'insurance-asset',
                    label: dictionary['navigation'].insuranceAsset,
                    href: '/company-data/assets/insurance'
                  }),
                  ...getFilteredMenu(permissionMap['/company-data/category/unit'], {
                    id: 'category-list-unit',
                    label: 'Kategori',
                    href: `/company-data/category/unit`
                  })
                ]
              },
              ...getFilteredMenu(['code.write'], {
                id: 'code',
                label: 'Kode',
                icon: 'fluens-number-symbol-16',
                children: [
                  {
                    id: 'component',
                    label: 'Component',
                    href: '/rnm/component'
                  },
                  {
                    id: 'job-code',
                    label: 'Job Code',
                    href: '/rnm/job-code'
                  },
                  {
                    id: 'kode-modifier',
                    label: 'Kode Modifier',
                    href: '/rnm/modifier'
                  }
                ]
              }),
              {
                id: 'unit-section',
                label: 'Unit',
                icon: 'car-unit',
                href: '/rnm/unit'
              },
              {
                id: 'fr-section',
                label: 'Field Report',
                icon: 'paste-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/fr/created'], {
                    id: 'created-fr',
                    label: 'Terbuat',
                    href: '/fr/created'
                  }),
                  ...getFilteredMenu(permissionMap['/fr/new-fr'], {
                    id: 'create-fr',
                    label: 'Buat',
                    href: '/fr/new-fr'
                  }),
                  ...getFilteredMenu(permissionMap['/fr/new-fr'], {
                    id: 'fr-draft',
                    label: 'Draft',
                    href: '/fr/draft'
                  })
                ]
              },
              {
                id: 'wo-section',
                label: 'Work Order',
                icon: 'wo-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/wo/list'], {
                    id: 'created-fr',
                    label: 'Report Masuk',
                    href: '/wo/list'
                  }),
                  ...getFilteredMenu(permissionMap['/wo/created'], {
                    id: 'wo-list',
                    label: 'Terbuat',
                    href: '/wo/created'
                  }),
                  ...getFilteredMenu(permissionMap['/wo/list'], {
                    id: 'wo-draft',
                    label: 'Draft',
                    href: '/wo/draft'
                  })
                ]
              },
              {
                id: 'work-process-section',
                label: 'Work Process',
                icon: 'work-process',
                children: [
                  ...getFilteredMenu(permissionMap['/wp-in'], {
                    id: 'work-process-in',
                    label: 'Masuk',
                    href: '/wp-in'
                  }),
                  ...getFilteredMenu(permissionMap['/wp/list'], {
                    id: 'work-process',
                    label: 'Terbuat',
                    href: '/wp/list'
                  }),
                  ...getFilteredMenu(permissionMap['/wp/create'], {
                    id: 'created-wp',
                    label: 'Buat',
                    href: '/wp/create'
                  }),
                  ...getFilteredMenu(permissionMap['/wp/create'], {
                    id: 'wp-draft',
                    label: 'Draft',
                    href: '/wp/draft'
                  }),
                  ...getFilteredMenu(permissionMap['/wp/review'], {
                    id: 'wp-review',
                    label: 'Review',
                    href: '/wp/review'
                  })
                ]
              },
              {
                id: 'part-swap-section',
                label: 'Part Swap',
                icon: 'part-swap-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/part-swap/list'], {
                    id: 'part-swap-list',
                    label: 'Terbuat',
                    href: '/part-swap/list'
                  }),
                  ...getFilteredMenu(permissionMap['/part-swap/create'], {
                    id: 'part-swap-create',
                    label: 'Buat Request',
                    href: '/part-swap/create'
                  }),
                  ...getFilteredMenu(permissionMap['/part-swap/draft'], {
                    id: 'part-swap-draft',
                    label: 'Draft',
                    href: '/part-swap/draft'
                  })
                ]
              },
              {
                id: 'pre-release-section',
                label: 'Pre-Release',
                icon: 'pre-release-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/wo/format-pre-release'], {
                    id: 'pre-release-format',
                    label: 'Format',
                    href: '/wo/format-pre-release'
                  }),
                  ...getFilteredMenu(permissionMap['/wo/pre-release-created'], {
                    id: 'pre-release-created',
                    label: 'Terbuat',
                    href: '/wo/pre-release-created'
                  }),
                  ...getFilteredMenu(permissionMap['/wo/pre-releases'], {
                    id: 'pre-release-list',
                    label: 'Pengajuan',
                    href: '/wo/pre-releases'
                  }),
                  ...getFilteredMenu(
                    permissionMap['/wo/unit-taking'],

                    {
                      id: 'unit-taking',
                      label: 'Pengambilan Unit',
                      href: '/wo/unit-taking'
                    }
                  )
                ]
              },
              {
                id: 'service-request-section',
                label: 'Service Request',
                icon: 'sr-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/service-request/list'], {
                    id: 'service-request-list',
                    label: 'Terbuat',
                    href: '/service-request/list'
                  }),
                  ...getFilteredMenu(permissionMap['/service-request/create'], {
                    id: 'service-request-create',
                    label: 'Buat Request',
                    href: '/service-request/create'
                  }),
                  ...getFilteredMenu(permissionMap['/service-request/draft'], {
                    id: 'service-request-draft',
                    label: 'Draft',
                    href: '/service-request/draft'
                  })
                ]
              }
            ]
          },
          ...getFilteredMenu(permissionMap['/accounting/accounts'], {
            id: 'accounting-section',
            isSection: true,
            label: 'Accounting',
            children: [
              {
                id: 'accounting',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/accounting`
              },
              {
                id: 'data-accounting',
                label: 'Akun & Data',
                icon: 'ri-building-4-line',
                children: [
                  ...getFilteredMenu(permissionMap['/accounting/accounts'], {
                    id: 'accounts-section',
                    label: 'Akun Perkiraan',
                    href: '/accounting/accounts'
                  }),
                  // ...getFilteredMenu(permissionMap['/accounting/salary'], {
                  //   id: 'salary-section',
                  //   label: 'Gaji / Tunjangan',
                  //   href: '/accounting/salary'
                  // }),
                  ...getFilteredMenu(permissionMap['/accounting/tax'], {
                    id: 'tax-section',
                    label: 'Pajak',
                    href: '/accounting/tax'
                  }),
                  ...getFilteredMenu(permissionMap['/accounting/currency'], {
                    id: 'currency-section',
                    label: 'Mata Uang',
                    href: '/accounting/currency'
                  }),
                  // ...getFilteredMenu(permissionMap['/accounting/payment-terms'], {
                  //   id: 'payment-terms-section',
                  //   label: 'Syarat Pembayaran',
                  //   href: '/accounting/payment-terms'
                  // }),
                  ...getFilteredMenu(permissionMap['/accounting/customer'], {
                    id: 'customer-section',
                    label: 'Customer/Pelanggan',
                    href: '/accounting/customer'
                  })
                  // ...getFilteredMenu(permissionMap['/accounting/carrier'], {
                  //   id: 'carrier-section',
                  //   label: 'Pengiriman',
                  //   href: '/accounting/carrier'
                  // })
                ]
              },
              {
                id: 'end-periods-section',
                label: 'End Period',
                icon: 'ri-calendar-event-line',
                children: [
                  ...getFilteredMenu(permissionMap['/accounting/end-periods'], {
                    id: 'end-periods',
                    label: 'List Period',
                    href: '/accounting/end-periods/list'
                  }),
                  ...getFilteredMenu(permissionMap['/accounting/end-periods'], {
                    id: 'end-periods',
                    label: 'Draft',
                    href: '/accounting/end-periods/draft'
                  })
                ]
              },
              ...getFilteredMenu(['asset.write'], {
                id: 'asset-mgmt',
                label: 'Manajemen Aset',
                icon: 'wealth-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/accounting/assets/list'], {
                    id: 'asset-list',
                    label: 'List Aset',
                    href: '/accounting/assets/list'
                  })
                  // ...getFilteredMenu(['asset.write'], {
                  //   id: 'asset-mobilization',
                  //   label: 'Mobilisasi Aset',
                  //   href: '/accounting/assets/mobilization'
                  // })
                ]
              }),
              {
                id: 'bookkeeping',
                label: 'Jurnal Umum',
                icon: 'journal-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/accounting/general-ledger/list'], {
                    id: 'general-ledger',
                    label: 'Terbuat',
                    href: '/accounting/general-ledger/list'
                  }),
                  ...getFilteredMenu(permissionMap['/accounting/general-ledger/create'], {
                    id: 'general-ledger',
                    label: 'Buat',
                    href: '/accounting/general-ledger/create'
                  }),
                  ...getFilteredMenu(permissionMap['/accounting/general-ledger/draft'], {
                    id: 'general-ledger',
                    label: 'Draft',
                    href: '/accounting/general-ledger/draft'
                  })
                ]
              },
              ...getFilteredMenu(
                [DefaultApprovalScope.PurchaseInvoice],
                {
                  id: 'purchase-invoice',
                  label: dictionary['navigation'].purchaseInvoice,
                  icon: 'invoice-icon',
                  children: [
                    ...getFilteredMenu(permissionMap['/purchase-invoice/list'], {
                      id: 'pi-created',
                      label: 'Terbuat',
                      href: `/purchase-invoice/list`
                    }),
                    ...getFilteredMenu(permissionMap['/purchase-invoice/create'], {
                      id: 'pi-list',
                      label: 'Buat',
                      href: `/purchase-invoice/create`
                    }),
                    ...getFilteredMenu(permissionMap['/purchase-invoice/draft'], {
                      id: 'pi-draft',
                      label: 'Draft',
                      href: `/purchase-invoice/draft`
                    })
                  ]
                },
                true
              ),
              {
                id: 'sales-invoice',
                label: 'Faktur Penjualan',
                icon: 'icon-money-purchase',
                children: [
                  ...getFilteredMenu(permissionMap['/sales-invoice/list'], {
                    id: 'sales-invoice',
                    label: 'Faktur Terbuat',
                    href: '/sales/invoice/list'
                  }),
                  ...getFilteredMenu(permissionMap['/sales-invoice/create'], {
                    id: 'sales-invoice-create',
                    label: 'Buat Faktur',
                    href: '/sales/invoice/create'
                  }),
                  ...getFilteredMenu(permissionMap['/sales-invoice/draft'], {
                    id: 'sales-invoice-draft',
                    label: 'Draft Faktur',
                    href: '/sales/invoice/draft'
                  })
                ]
              },
              {
                id: 'cash-bank',
                label: 'Kas & Bank',
                icon: 'cash-bank-icon',
                children: [
                  ...getFilteredMenu(permissionMap['/cash-bank/purchase-invoice'], {
                    id: 'purchase-invoice',
                    label: 'Hutang Pembelian',
                    href: '/cash-bank/purchase-invoice'
                  }),
                  ...getFilteredMenu(['payment.create'], {
                    id: 'payments',
                    label: 'Pembayaran',
                    children: [
                      ...getFilteredMenu(permissionMap['/cash-bank/payments/list'], {
                        id: 'payment-list',
                        label: 'Terbuat',
                        href: '/cash-bank/payments/list'
                      }),
                      ...getFilteredMenu(permissionMap['/cash-bank/payments/create'], {
                        id: 'payment-create',
                        label: 'Buat',
                        href: '/cash-bank/payments/create'
                      }),
                      ...getFilteredMenu(permissionMap['/cash-bank/payments/draft'], {
                        id: 'payment-draft',
                        label: 'Draft',
                        href: '/cash-bank/payments/draft'
                      })
                    ]
                  }),
                  ...getFilteredMenu(permissionMap['/cash-bank/sales-invoice'], {
                    id: 'sales-invoice',
                    label: 'Piutang Penjualan',
                    href: '/cash-bank/sales-invoice'
                  }),
                  ...getFilteredMenu(['cash-receipt.create'], {
                    id: 'receipt',
                    label: 'Penerimaan',
                    children: [
                      ...getFilteredMenu(permissionMap['/cash-bank/receipt/list'], {
                        id: 'cash-receipt-list',
                        label: 'Terbuat',
                        href: '/cash-bank/receipt/list'
                      }),
                      ...getFilteredMenu(permissionMap['/cash-bank/receipt/create'], {
                        id: 'cash-receipt-create',
                        label: 'Buat',
                        href: '/cash-bank/receipt/create'
                      }),
                      ...getFilteredMenu(permissionMap['/cash-bank/receipt/draft'], {
                        id: 'cash-receipt-draft',
                        label: 'Draft',
                        href: '/cash-bank/receipt/draft'
                      })
                    ]
                  }),
                  {
                    id: 'reconciliation',
                    label: 'Rekonsiliasi',
                    children: [
                      ...getFilteredMenu(permissionMap['/reconciliation'], {
                        id: 'reconciliation-list',
                        label: 'Terbuat',
                        href: '/reconciliation/list'
                      }),
                      ...getFilteredMenu(permissionMap['/reconciliation'], {
                        id: 'reconciliation-create',
                        label: 'Buat',
                        href: '/reconciliation/create'
                      }),
                      ...getFilteredMenu(permissionMap['/reconciliation/draft'], {
                        id: 'reconciliation-draft',
                        label: 'Draft',
                        href: '/reconciliation/draft'
                      })
                    ]
                  }
                ]
              },
              {
                id: 'reports-section',
                label: 'Laporan',
                icon: 'icon-report',
                children: [
                  {
                    id: 'keuangan',
                    label: 'Keuangan',
                    href: '/report/finance'
                  },
                  {
                    id: 'buku-besar',
                    label: 'Buku Besar',
                    href: '/report/ledger'
                  },
                  {
                    id: 'cash-bank',
                    label: 'Kas & Bank',
                    href: '/report/cash-bank'
                  },
                  {
                    id: 'receivables-customers',
                    label: 'Piutang & Pelanggan',
                    href: '/report/receivables-customers'
                  },
                  {
                    id: 'debt-suppliers',
                    label: 'Hutang & Pelanggan',
                    href: '/report/debt-suppliers'
                  }
                ]
              }
            ]
          }),
          ...getFilteredMenu(permissionMap['/company-data/category'], {
            id: 'data-section',
            isSection: true,
            label: dictionary['navigation'].dataPerusahaan,
            children: [
              ...getFilteredMenu(permissionMap['/company-data/category'], {
                id: 'category-list',
                label: 'List Kategori',
                icon: 'tabler--category',
                children: []
              }),
              ...getFilteredMenu(permissionMap['/company-data/site'], {
                id: 'site-list',
                label: dictionary['navigation'].listSite,
                icon: 'ri-map-pin-line',
                href: '/company-data/site'
              }),
              ...getFilteredMenu(permissionMap['/company-data/projects'], {
                id: 'project-list',
                label: 'Manajemen Proyek',
                href: '/company-data/projects',
                icon: 'projects-icon'
              }),
              {
                id: 'department',
                label: 'Departemen',
                icon: 'ri-building-4-line',
                children: [
                  ...getFilteredMenu(permissionMap['/company-data/department'], {
                    id: 'department-list',
                    label: dictionary['navigation'].listDepartemen,
                    href: '/company-data/department'
                  }),
                  ...getFilteredMenu(permissionMap['/company-data/division'], {
                    id: 'division-list',
                    label: 'List Divisi',
                    href: '/company-data/division'
                  })
                ]
              }
            ]
          }),
          {
            id: 'export-import-section',
            isSection: true,
            label: dictionary['navigation'].exportImport,
            children: [
              {
                id: 'data-export',
                label: 'List Ekspor Data',
                icon: 'tabler-file-export',
                href: '/data-export'
              },
              {
                id: 'data-import',
                label: 'List Impor Data',
                icon: 'tabler-file-import',
                href: '/data-import'
              }
            ]
          },
          ...getFilteredMenu(
            [
              ...permissionMap['/setting/numbering'],
              ...permissionMap['/user/list'],
              ...permissionMap['/user/role'],
              ...permissionMap['/company-data/titles']
            ],
            {
              id: 'settings-section',
              isSection: true,
              label: dictionary['navigation'].settings,
              children: [
                {
                  id: 'user',
                  label: dictionary['navigation'].user,
                  icon: 'ri-user-line',
                  children: [
                    ...getFilteredMenu(permissionMap['/user/list'], {
                      id: 'user-list',
                      label: dictionary['navigation'].listUser,
                      href: `/user/list`
                    }),
                    ...getFilteredMenu(permissionMap['/user/role'], {
                      id: 'role',
                      label: dictionary['navigation'].roleUser,
                      href: `/user/role`
                    }),
                    ...getFilteredMenu(permissionMap['/company-data/titles'], {
                      id: 'titles-section',
                      label: 'List Jabatan',
                      href: '/company-data/titles'
                    })
                  ]
                },
                ...getFilteredMenu(permissionMap['/setting/numbering'], {
                  id: 'numbering-settings',
                  label: 'Penomoran',
                  icon: 'lsicon--number-filled',
                  href: '/setting/numbering'
                })
              ]
            }
          )
        ]
      : ([] as MenuDataType[])

  // Helper function to check if user has permission for a URL by checking permissions directly
  const hasPermissionForUrl = useCallback(
    (url: string): boolean => {
      const requiredPermissions = permissionMap[url]
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return true // No permissions required for this URL
      }

      // Check if user has any of the required permissions
      return requiredPermissions.some(
        permission => accountPermissions?.includes(permission) || offlinePermissions?.includes(permission)
      )
    },
    [accountPermissions, offlinePermissions]
  )

  const verticalNavProviderValue = useMemo(
    () => ({
      ...verticalNavState,
      updateVerticalNavState,
      collapseVerticalNav,
      hoverVerticalNav,
      toggleVerticalNav,
      confirmState,
      setConfirmState,
      menuData,
      hasPermissionForUrl
    }),
    [
      verticalNavState,
      updateVerticalNavState,
      collapseVerticalNav,
      hoverVerticalNav,
      toggleVerticalNav,
      confirmState,
      setConfirmState,
      menuData,
      hasPermissionForUrl
    ]
  )

  return (
    <MenuContext.Provider value={verticalNavProviderValue}>
      {children}
      {confirmState.open && (
        <ConfirmDialog
          title={confirmState.title}
          content={confirmState.content}
          cancelText={confirmState.cancelText}
          confirmText={confirmState.confirmText}
          confirmColor={confirmState.confirmColor}
          open={confirmState.open}
          setOpen={open => setConfirmState(current => ({ ...current, open }))}
          onConfirm={confirmState.onConfirm}
          onCancel={confirmState.onCancel}
        />
      )}
    </MenuContext.Provider>
  )
}

export default MenuContext
