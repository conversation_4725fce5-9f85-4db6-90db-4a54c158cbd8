// React Imports
import { useEffect, useRef } from 'react'
import type { CSSProperties } from 'react'

// Third-party Imports
import styled from '@emotion/styled'

// Type Imports
import { colors } from '@mui/material'

import type { MenuContextProps } from '@/components/menu/contexts/menuContext'

// Component Imports
import MaterializeLogo from '@/core/svg/Logo'

// Config Imports
import themeConfig from '@/configs/themeConfig'

// Hook Imports
import useVerticalNav from '@/components/menu/hooks/useVerticalNav'
import { useSettings } from '@/core/hooks/useSettings'
import { useRouter } from '@/routes/hooks'

type LogoTextProps = {
  isHovered?: MenuContextProps['isHovered']
  isCollapsed?: MenuContextProps['isCollapsed']
  transitionDuration?: MenuContextProps['transitionDuration']
  color?: CSSProperties['color']
}

const LogoText = styled.span<LogoTextProps>`
  font-size: 1.5rem;
  line-height: 1.2;
  font-weight: 600;
  letter-spacing: 0.15px;
  text-transform: capitalize;
  color: var(--mui-palette-text-primary);
  cursor: pointer;
  color: ${({ color }) => color ?? 'var(--mui-palette-text-primary)'};
  transition: ${({ transitionDuration }) =>
    `margin-inline-start ${transitionDuration}ms ease-in-out, opacity ${transitionDuration}ms ease-in-out`};

  ${({ isHovered, isCollapsed }) => (isCollapsed && !isHovered ? 'opacity: 0; margin-inline-start: 0;' : 'opacity: 1;')}
`

const Logo = () => {
  // Refs
  const logoTextRef = useRef<HTMLSpanElement>(null)
  const router = useRouter()

  // Hooks
  const { isHovered, transitionDuration } = useVerticalNav()
  const { settings } = useSettings()

  // Vars
  const { layout } = settings

  useEffect(() => {
    if (layout !== 'collapsed') {
      return
    }

    if (logoTextRef && logoTextRef.current) {
      if (layout === 'collapsed' && !isHovered) {
        logoTextRef.current?.classList.add('hidden')
      } else {
        logoTextRef.current.classList.remove('hidden')
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isHovered, layout])

  return (
    <div className='flex items-center min-bs-[24px]'>
      {/* <MaterializeLogo /> */}
      <LogoText
        ref={logoTextRef}
        isHovered={isHovered}
        isCollapsed={layout === 'collapsed'}
        transitionDuration={transitionDuration}
        onClick={() => router.replace('/')}
      >
        {themeConfig.appName}
      </LogoText>
    </div>
  )
}

export default Logo
