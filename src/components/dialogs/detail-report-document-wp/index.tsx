import { useDeleteReportWp } from '@/api/services/wp/mutation'
import { WpQueryMethods } from '@/api/services/wp/query'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useWp } from '@/pages/repair-and-maintenance/wp/context/WpContext'
import { truncateFromEnd } from '@/pages/repair-and-maintenance/wp/detail/config/utils'
import { WpReportType } from '@/types/wpTypes'
import {
  Avatar,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { toast } from 'react-toastify'

type DetailReportDocumentWpProps = {
  open: boolean
  setOpen: (open: boolean) => void
  report?: WpReportType
  onEditReport?: (report: WpReportType) => void
}

const DetailReportDocumentWp = ({ open, setOpen, report, onEditReport = () => {} }: DetailReportDocumentWpProps) => {
  const { setConfirmState } = useMenu()
  const { refetchWpReports } = useWp()

  const handleClose = () => {
    setOpen(false)
  }

  const { data: reportDetail } = useQuery({
    enabled: !!report?.id,
    queryKey: ['WP_REPORT_QUERY_KEY', report?.id],
    queryFn: () => WpQueryMethods.getWpReport({ reportId: report?.id, workProcessId: report?.workProcessId })
  })

  const { mutate: deleteReport, isLoading: deleteReportLoading } = useDeleteReportWp()

  const handleDeleteReport = () => {
    setConfirmState({
      open: true,
      title: 'Hapus Laporan',
      content: 'Apakah kamu yakin ingin menghapus laporan ini?',
      confirmText: 'Hapus',
      onConfirm: () => {
        deleteReport(
          {
            reportId: report.id,
            workProcessId: report?.workProcessId
          },
          {
            onSuccess: () => {
              toast.success('Berhasil menghapus laporan')
              refetchWpReports()
              setTimeout(() => {
                handleClose()
              }, 1000)
            }
          }
        )
      }
    })
  }

  return (
    <Dialog maxWidth='sm' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Detil Laporan
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <Typography variant='caption'>Nama Laporan</Typography>
              <Typography>{report?.name}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <Typography variant='caption'>Waktu Laporan</Typography>
              <Typography>
                {report?.createdAt ? formatDate(new Date(report?.createdAt), 'dd/MM/yyyy, HH:mm', { locale: id }) : '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <Typography variant='caption'>Dokumen</Typography>
              <div className='flex justify-between items-center'>
                <Typography>{truncateFromEnd(report?.reportUrl, 15)}</Typography>
                <Button href={report?.reportUrl} download size='small' variant='outlined'>
                  Unduh
                </Button>
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <Typography variant='caption'>Catatan</Typography>
              <Typography>{report?.note}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <Typography variant='caption'>Foto Proses Pengerjaan</Typography>
              <div className='flex flex-wrap gap-3 w-full'>
                {reportDetail?.images?.map(image => (
                  <img className='flex-1 max-w-[80px] rounded-[16px]' key={image.id} alt={image.id} src={image.url} />
                ))}
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <Typography variant='caption'>Ditambahkan oleh</Typography>
              <div className='flex gap-2 items-center pl-4'>
                <Avatar src={report?.createdByUser?.profilePictureUrl} />
                <div className='p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {report?.createdByUser?.fullName}
                    </p>
                  </div>
                  <div className='flex justify-between w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        {report?.createdByUser?.title}
                      </small>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={handleDeleteReport} color='error' variant='outlined'>
          Hapus Laporan
        </Button>
        <Button onClick={() => onEditReport(reportDetail)} variant='contained'>
          Edit Laporan
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DetailReportDocumentWp
