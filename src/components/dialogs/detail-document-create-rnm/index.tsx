import ChevronRight from '@/components/menu/svg/ChevronRight'
import { useWo } from '@/pages/repair-and-maintenance/wo/context/WoContext'
import { WoSegmentType } from '@/types/woTypes'
import { Box, Dialog, DialogContent, DialogTitle, Grid, IconButton, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useLocation, useNavigate } from 'react-router-dom'
import { tableColumns } from './table'
import { useMemo } from 'react'
import Table from '@/components/table'
import { toast } from 'react-toastify'

interface DetailDocumentComponentDialogProps {
  open: boolean
  setOpen: (value: React.SetStateAction<any>) => void
  handleClose: () => void
  segment: WoSegmentType
}

const DetailDocumentCreateDialog = (props: DetailDocumentComponentDialogProps) => {
  const { activeSegment } = useWo()
  const { open, handleClose, setOpen, segment } = props
  const location = useLocation()
  const navigate = useNavigate()

  const tableOptions = useMemo(
    () => ({
      data: [activeSegment],
      columns: tableColumns,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [activeSegment]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Buat Dokumen
        <Typography component='span' className='flex flex-col text-center'>
          Pilih jenis dokumen yang akan dibuat dari Segment ini
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4 select-none'>
        <IconButton onClick={() => setOpen(null)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Box className='shadow-md rounded-[8px]' sx={{ mb: 8 }}>
          <Table
            headerColor='green'
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography>Belum ada data</Typography>
              </td>
            }
            disablePagination
          />
        </Box>
        <Grid container spacing={5} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <div
              role='button'
              onClick={() => navigate(`${location.pathname}/${activeSegment?.id}/create-mr`)}
              className='flex items-center justify-between gap-2 cursor-pointer'
            >
              <i className='ic-outline-file-upload size-6' />
              <div className='flex flex-col w-[80%]'>
                <Typography>Buat Material Request</Typography>
                <Typography variant='caption'>
                  Pilih jika dokumen akan diajukan untuk permintaan material / barang
                </Typography>
              </div>
              <ChevronRight />
            </div>
          </Grid>
          <Grid item xs={12}>
            <div
              role='button'
              onClick={() => {
                navigate(`${location.pathname}/${activeSegment?.id}/create-sr`)
              }}
              className='flex items-center justify-between gap-2 cursor-pointer'
            >
              <i className='sr-icon size-6' />
              <div className='flex flex-col w-[80%]'>
                <Typography>Buat Service Request</Typography>
                <Typography variant='caption'>
                  Pilih jika dokumen akan diajukan untuk permintaan service dengan mendatangkan staf ke lapangan/site
                </Typography>
              </div>
              <ChevronRight />
            </div>
          </Grid>
          <Grid item xs={12}>
            <div
              role='button'
              onClick={() => {
                navigate(`${location.pathname}/${activeSegment?.id}/create-part-swap`)
              }}
              className='flex items-center justify-between gap-2 cursor-pointer'
            >
              <i className='part-swap-icon size-6' />
              <div className='flex flex-col w-[80%]'>
                <Typography>Buat Part Swap Request</Typography>
                <Typography variant='caption'>
                  Pilih jika dokumen akan diajukan untuk permintaan penukaran part antar unit yyang sudah terdaftar
                </Typography>
              </div>
              <ChevronRight />
            </div>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default DetailDocumentCreateDialog
