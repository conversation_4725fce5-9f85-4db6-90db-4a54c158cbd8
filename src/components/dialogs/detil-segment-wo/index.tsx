import RnMQueryMethods from '@/api/services/rnm/query'
import { getStatusConfig } from '@/pages/repair-and-maintenance/wo/fr-list/config/utils'
import {
  Chip,
  Dialog,
  DialogContent,
  DialogProps,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import { tableColumns } from './config'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { WpQueryMethods } from '@/api/services/wp/query'
import ChevronRight from '@/components/menu/svg/ChevronRight'
import { useLocation, useNavigate } from 'react-router-dom'
import { WoSegmentItem } from '@/types/woTypes'

type DetilSegmentWoProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onClose?: () => void
  selectedSegmentId?: string
  onItemClick?: (item: WoSegmentItem) => void
  woId?: string
}

const DetilSegmentWo = (props: DetilSegmentWoProps) => {
  const { open, setOpen } = props
  const navigate = useNavigate()
  const { pathname } = useLocation()

  const isFromWp = pathname.includes('wp')

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    props?.onClose?.()
    setOpen(false)
  }

  const { data: segmentDetail } = useQuery({
    enabled: !!props?.selectedSegmentId && !!props?.woId,
    queryKey: ['WO_SEGMENT_KEY', props.selectedSegmentId, props.woId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoSegment(props.woId, props.selectedSegmentId)
      return res
    }
  })

  const { data: wpList } = useQuery({
    enabled: !!props?.selectedSegmentId && !!props?.woId,
    queryKey: ['WP_LIST_KEY', props.selectedSegmentId, props.woId],
    queryFn: async () => {
      const res = await WpQueryMethods.getWpList({
        workOrderSegmentId: props.selectedSegmentId,
        limit: Number.MAX_SAFE_INTEGER
      })
      return res.items
    }
  })

  const componentTableOptions = useMemo(
    () => ({
      data: segmentDetail?.items.filter(i => i.type === 'COMPONENT') ?? [],
      columns: tableColumns({
        type: 'COMPONENT',
        detail: item => {
          props.onItemClick?.(item)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [segmentDetail?.items]
  )

  const miscellaneousTableOptions = useMemo(
    () => ({
      data: segmentDetail?.items.filter(i => i.type !== 'COMPONENT') ?? [],
      columns: tableColumns({
        type: 'MISCELLANEOUS',
        detail: item => {
          props.onItemClick?.(item)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [segmentDetail?.items]
  )

  const tableParts = useReactTable<any>(componentTableOptions)
  const tableMisc = useReactTable<any>(miscellaneousTableOptions)

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Segment
        <Typography component='span' className='flex flex-col text-center'>
          Lihat detil data Segment
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-8 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          {!isFromWp && (
            <Grid item xs={12}>
              <div className='flex bg-[#4C4E640D] p-4 justify-between rounded-[10px]'>
                <div className='space-y-3'>
                  <Typography>Status</Typography>
                  {wpList?.length > 0 ? (
                    <Typography
                      color='primary'
                      role='button'
                      sx={{ cursor: 'pointer', display: 'inline-flex', alignItems: 'center' }}
                      onClick={() => navigate(`/wp/list/${wpList[0].id}`)}
                    >
                      Lihat Dokumen Work Process <ChevronRight />
                    </Typography>
                  ) : (
                    <Typography variant='caption'>Belum ada Work Proses</Typography>
                  )}
                </div>
                <Chip
                  label={getStatusConfig(segmentDetail?.status).label}
                  color={getStatusConfig(segmentDetail?.status).color as any}
                  size='small'
                  variant='tonal'
                />
              </div>
            </Grid>
          )}
          <Grid item xs={12}>
            <Typography>Detil Segment</Typography>
          </Grid>
          <Grid item xs={12}>
            <Grid container justifyContent='space-around'>
              <Grid item xs={12} md={4}>
                <div className='flex flex-col'>
                  <Typography variant='caption'>Job Code</Typography>
                  <Typography>
                    {segmentDetail?.jobCode?.code
                      ? `${segmentDetail?.jobCode?.code} | ${segmentDetail?.jobCode?.description}`
                      : '-'}
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={12} md={4}>
                <div className='flex flex-col'>
                  <Typography variant='caption'>SMCS</Typography>
                  <Typography>
                    {segmentDetail?.componentCode?.code
                      ? `${segmentDetail?.componentCode?.code} | ${segmentDetail?.componentCode?.description}`
                      : '-'}
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={12} md={4}>
                <div className='flex flex-col'>
                  <Typography variant='caption'>Modifier</Typography>
                  <Typography>
                    {segmentDetail?.modifierCode?.code
                      ? `${segmentDetail?.modifierCode?.code} | ${segmentDetail?.modifierCode?.description}`
                      : '-'}
                  </Typography>
                </div>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <Typography>Parts & Komponen</Typography>
          </Grid>
          <Grid item xs={12}>
            <div className='shadow-sm rounded-[8px]'>
              <Table
                table={tableParts}
                emptyLabel={
                  <td colSpan={tableParts.getVisibleFlatColumns().length} className='text-center h-60'>
                    <Typography>Belum ada Barang</Typography>
                    <Typography className='text-sm text-gray-400'>
                      Tambahkan barang yang ingin dimasukkan dalam Segment ini
                    </Typography>
                  </td>
                }
                disablePagination
              />
            </div>
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <Typography>Miscellaneous</Typography>
          </Grid>
          <Grid item xs={12}>
            <div className='shadow-sm rounded-[8px]'>
              <Table
                table={tableMisc}
                emptyLabel={
                  <td colSpan={tableMisc.getVisibleFlatColumns().length} className='text-center h-60'>
                    <Typography>Belum ada Barang</Typography>
                    <Typography className='text-sm text-gray-400'>
                      Tambahkan barang yang ingin dimasukkan dalam Segment ini
                    </Typography>
                  </td>
                }
                disablePagination
              />
            </div>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default DetilSegmentWo
