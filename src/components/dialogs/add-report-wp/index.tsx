// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Grid } from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

import PhotoPicker from '@/components/PhotoPicker'
import { ImageItemType } from '@/types/companyTypes'
import { array, object, string, TypeOf, enum as enum_ } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useState } from 'react'
import { useUploadDocument, useUploadImage } from '@/api/services/file/mutation'
import { toast } from 'react-toastify'
import LoadingButton from '@mui/lab/LoadingButton'
import { useFilePicker } from 'use-file-picker'
import { useAddReportWp, useUpdateReportWp } from '@/api/services/wp/mutation'
import { useParams } from 'react-router-dom'
import { format } from 'date-fns'
import { WpReportType } from '@/types/wpTypes'
import { truncateFromEnd } from '@/pages/repair-and-maintenance/wp/detail/config/utils'

type AddReportDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSuccessAdd?: () => void
  report?: WpReportType
}

export const defaultImageList = new Array(5).fill({
  content: '',
  fileName: ''
}) as ImageItemType[]

const addReportSchema = object({
  id: string().optional().nullable(),
  wpId: string().optional().nullable(),
  mode: enum_(['ADD', 'UPDATE']).optional().nullable(),
  name: string(),
  uploadId: string().optional().nullable(),
  docName: string().optional().nullable(),
  note: string(),
  images: array(object({ uploadId: string() })).min(1)
}).refine(
  data => {
    if (data.mode === 'UPDATE') return true
    return data.uploadId !== undefined && data.uploadId !== null && data.uploadId !== ''
  },
  { path: ['uploadId'] }
)

type AddReportInput = Required<TypeOf<typeof addReportSchema>>

const AddReportDialog = ({ open, setOpen, onSuccessAdd = () => {}, report }: AddReportDialogProps) => {
  const params = useParams()
  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)
  const { resetField, reset, control, handleSubmit, getValues } = useForm<AddReportInput>({
    resolver: zodResolver(addReportSchema),
    defaultValues: {
      images: [],
      mode: 'ADD'
    }
  })

  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/*'],
    readAs: 'DataURL'
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutateAsync: uploadDocumentMutate, isLoading: uploadDocumentLoading } = useUploadDocument()
  const { mutate: addReportWp, isLoading: addReportWpLoading } = useAddReportWp()
  const { mutate: updateReportWp, isLoading: updateReportWpLoading } = useUpdateReportWp()

  const isLoading = uploadLoading || addReportWpLoading || uploadDocumentLoading || updateReportWpLoading

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<AddReportInput> = (inputValues: AddReportInput) => {
    if (inputValues.mode === 'ADD') {
      uploadDocumentMutate({
        fieldName: `work_process_report_file_${format(new Date(), 'yyyyMMddHHmmss')}`,
        file: filesContent?.[0]?.content,
        scope: 'public-document',
        fileName: filesContent?.[0]?.name
      }).then(response => {
        const imageExists = imageList.filter(image => image?.fileName && image?.content)
        if (imageExists.length > 0) {
          Promise.all(
            imageExists.map(image =>
              uploadMutate({
                fieldName: `work_process_report_image_${format(new Date(), 'yyyyMMddHHmmss')}`,
                file: image.content,
                scope: 'public-image',
                fileName: image.fileName
              })
            )
          ).then(values => {
            const uploadIds = values.map(val => ({
              uploadId: val.data?.id ?? ''
            }))
            addReportWp(
              {
                name: inputValues.name,
                note: inputValues.note,
                images: uploadIds.map(id => ({ uploadId: id.uploadId })),
                uploadId: response?.data?.id,
                workProcessId: params?.wpId
              },
              {
                onSuccess: () => {
                  toast.success('Laporan Proses Pengerjaan Berhasil Dibuat')
                  setOpen(false)
                  onSuccessAdd()
                }
              }
            )
          })
        }
      })
    } else {
      updateReportWp(
        {
          name: inputValues.name,
          note: inputValues.note,
          reportId: inputValues.id,
          workProcessId: inputValues.wpId,
          images: undefined,
          uploadId: undefined
        },
        {
          onSuccess: () => {
            toast.success('Laporan Proses Pengerjaan Berhasil Diubah')
            setOpen(false)
            onSuccessAdd()
          }
        }
      )
    }
  }

  useEffect(() => {
    if (filesContent?.length > 0) {
      resetField('uploadId', { defaultValue: filesContent?.[0]?.name })
    }
  }, [filesContent])

  useEffect(() => {
    const imageExists = imageList.filter(image => image?.fileName)
    if (imageExists.length > 0) {
      resetField('images', { defaultValue: imageExists.map(img => ({ uploadId: img.fileName })) })
    }
  }, [imageList])

  useEffect(() => {
    if (report) {
      reset({
        id: report.id,
        wpId: report.workProcessId,
        mode: 'UPDATE',
        name: report.name,
        note: report.note,
        docName: truncateFromEnd(report.reportUrl, 15),
        images: report.images.map(img => ({ uploadId: `${img.id}` }))
      })
      setImageList([
        ...report.images.map(image => ({
          content: image.url,
          uploadId: image.id
        })),
        ...(new Array(5 - (report.images?.length ?? 0)).fill({
          content: '',
          fileName: ''
        }) as ImageItemType[])
      ])
    }
  }, [report])

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {!report ? 'Tambah' : 'Ubah'} Laporan Proses Pengerjaan
        {!report && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan laporan proses pengerjaan untuk WP ini
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='name'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  label='Nama Laporan'
                  value={value}
                  onChange={e => onChange(e.target.value)}
                  error={!!error}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <div className='flex items-center gap-4'>
                <Controller
                  control={control}
                  name='uploadId'
                  render={({ fieldState: { error } }) => (
                    <TextField
                      key={JSON.stringify(filesContent)}
                      label='Unggah Dokumen'
                      fullWidth
                      value={filesContent?.[0]?.name ?? getValues('docName')}
                      placeholder='Belum ada file dipilih'
                      aria-readonly
                      className='flex-1'
                      error={!!error}
                    />
                  )}
                />
                <Button disabled={isLoading} variant='contained' onClick={() => openFilePicker()}>
                  Unggah
                </Button>
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  label='Catatan'
                  value={value}
                  onChange={e => onChange(e.target.value)}
                  multiline
                  rows={3}
                  error={!!error}
                />
              )}
            />
          </Grid>
        </Grid>

        <Controller
          control={control}
          name='images'
          render={({ fieldState: { error } }) => (
            <Typography color={!!error ? 'error' : 'inherit'} variant='caption' mt={4} mb={3}>
              Foto Proses pengerjaan
            </Typography>
          )}
        />
        <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
          {imageList?.map((item, index) => (
            <PhotoPicker
              key={`${item.content}_${index}`}
              content={item.content}
              disabled={isLoading}
              onPicked={(content, fileName) => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content, fileName }
                  return tempCurrent
                })
              }}
              onRemoved={() => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content: '', fileName: '' }
                  return tempCurrent
                })
              }}
            />
          ))}
        </div>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {!report ? 'Tambah' : 'Ubah'} Laporan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddReportDialog
