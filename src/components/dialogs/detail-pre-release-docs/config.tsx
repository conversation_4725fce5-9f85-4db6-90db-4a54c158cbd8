import { PreReleaseChecklistType } from '@/types/preReleaseTypes'
import { Checkbox, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<PreReleaseChecklistType>()

export const tableColumns = (withDetail?: boolean) => [
  columnHelper.display({
    id: 'number',
    header: 'NO',
    size: 5,
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('parameter.description', {
    header: 'DESKRIPSI',
    size: 350,
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography component='li'>{row.original.templateCheckPoint.name}</Typography>
        <Typography variant='body2' sx={{ ml: '20px', maxWidth: '400px', textWrap: 'balance' }}>
          {row.original.templateCheckPoint.description}
        </Typography>
      </div>
    )
  }),
  ...(withDetail
    ? [
        columnHelper.accessor('isChecked', {
          header: 'CHECK POINT',
          cell: ({ row }) => (
            <div className='flex justify-center w-1/3'>
              <Checkbox checked={row.original.isChecked} />
            </div>
          )
        }),
        columnHelper.accessor('note', {
          header: 'CATATAN',
          size: 350,
          cell: ({ row }) => (
            <div className='flex flex-col'>
              <Typography>{row.original.note ?? '-'}</Typography>
            </div>
          )
        })
      ]
    : [])
]
