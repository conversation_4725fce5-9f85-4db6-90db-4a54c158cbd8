import CompanyQueryMethods, { UNIT_QUERY_KEY } from '@/api/services/company/query'
import PreReleaseQueryMethods, { PRE_RELEASE_QUERY_KEY } from '@/api/services/pre-release/query'
import RnMQueryMethods from '@/api/services/rnm/query'
import { PreReleaseType } from '@/types/preReleaseTypes'
import { Dialog, DialogContent, DialogTitle, Divider, Grid, IconButton, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from './config'
import { useMemo } from 'react'
import Table from '@/components/table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { DEFAULT_CATEGORY } from '@/data/default/category'

type DialogDetilPreReleaseProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onClose?: () => void
  preReleaseData?: PreReleaseType
  detailed?: boolean
}

const DialogDetilPreRelease = ({
  open,
  setOpen,
  onClose = () => {},
  preReleaseData,
  detailed = false
}: DialogDetilPreReleaseProps) => {
  const preReleaseDetail = preReleaseData

  const { data: woDetail } = useQuery({
    enabled: !!preReleaseDetail?.workOrder?.id,
    queryKey: ['WO_QUERY_KEY', preReleaseDetail?.workOrder?.id],
    queryFn: () => RnMQueryMethods.getWoDetail(preReleaseDetail?.workOrder?.id)
  })

  const { data: unitDetail } = useQuery({
    enabled: !!woDetail?.unit?.id,
    queryKey: [UNIT_QUERY_KEY, woDetail?.unit?.id],
    queryFn: () => CompanyQueryMethods.getUnit(woDetail?.unit?.id)
  })

  const handleClose = () => {
    setOpen(false)
    onClose?.()
  }

  const tableOptions = useMemo(
    () => ({
      data: preReleaseDetail?.checkPoints ?? [],
      columns: tableColumns(detailed),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [preReleaseDetail]
  )
  const table = useReactTable<any>(tableOptions)

  return (
    <Dialog open={open} maxWidth='md' onClose={handleClose}>
      <DialogTitle variant='h5' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Dokumen Pre-Release
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-2 bg-[#4C4E640D] p-4 rounded-[8px]'>
              <Typography variant='h5'>Jenis Pekerjaan</Typography>
              <Typography>
                {preReleaseData?.workType === 'UNSCHEDULED' ? 'Unscheduled' : 'Scheduled'} Service
              </Typography>
              <Divider />
              <div className='flex flex-col gap-4'>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Kategori Pekerjaan</Typography>
                  <Typography>{preReleaseData?.workName ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Format Dokumen</Typography>
                  <Typography>{preReleaseData?.template?.title ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Dibuat Oleh</Typography>
                  <div className='flex justify-between'>
                    <Typography>{preReleaseDetail?.createdByUser?.fullName ?? '-'}</Typography>
                    <small>
                      {preReleaseDetail?.createdAt
                        ? formatDate(new Date(preReleaseDetail?.createdAt), 'dd/MM/yyyy, HH:mm', {
                            locale: id
                          })
                        : '-'}
                    </small>
                  </div>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Diassign ke</Typography>
                  <Typography>{preReleaseDetail?.assignedToUser?.fullName ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Dicek Oleh</Typography>
                  <div className='flex justify-between'>
                    <Typography>{preReleaseDetail?.checkedByUser?.fullName ?? '-'}</Typography>
                    <small>
                      {preReleaseDetail?.checkedAt
                        ? formatDate(new Date(preReleaseDetail?.checkedAt), 'dd/MM/yyyy, HH:mm', {
                            locale: id
                          })
                        : '-'}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <div className='bg-[#4C4E640D] p-4 rounded-[8px] space-y-4'>
                  <Typography variant='h5'>Detil Unit</Typography>
                  <div className='grid grid-cols-2 gap-x-2 gap-y-4'>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Kode Unit</Typography>
                      <Typography>{unitDetail?.number ?? '-'}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Kode Activa</Typography>
                      <Typography>{unitDetail?.asset?.code ?? '-'}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Kategori Unit</Typography>
                      <Typography>{unitDetail?.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Jenis Unit</Typography>
                      <Typography>{unitDetail?.subCategory?.name}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Type Equipment</Typography>
                      <Typography>{unitDetail?.equipmentType ?? '-'}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Merk Unit</Typography>
                      <Typography>{unitDetail?.brandName}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Tipe Unit</Typography>
                      <Typography>{unitDetail?.type}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Nomor Lambung</Typography>
                      <Typography>{unitDetail?.hullNumber}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Nomor Rangka</Typography>
                      <Typography>{unitDetail?.chassisNumber}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Nomor Mesin</Typography>
                      <Typography>{unitDetail?.engineNumber ?? '-'}</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Plat Nomor</Typography>
                      <Typography>{unitDetail?.plateNumber ?? '-'}</Typography>
                    </div>
                  </div>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='bg-[#4C4E640D] p-4 rounded-[8px] space-y-4'>
                  <Typography variant='h5'>Data Unit</Typography>
                  <div className='grid grid-cols-2 gap-x-2 gap-y-4'>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>KM</Typography>
                      <Typography>{woDetail?.unitKm} KM</Typography>
                    </div>
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>HM</Typography>
                      <Typography>{woDetail?.unitHm} Jam</Typography>
                    </div>
                  </div>
                </div>
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <div className='shadow-sm rounded-sm'>
              <Table
                table={table}
                emptyLabel={
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                    <Typography>Belum ada Checklist</Typography>
                  </td>
                }
                disablePagination
                headerColor='green'
              />
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2'>
              <Typography variant='caption'>Catatan</Typography>
              <Typography>{preReleaseDetail?.note}</Typography>
            </div>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default DialogDetilPreRelease
