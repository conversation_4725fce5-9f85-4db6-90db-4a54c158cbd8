import { StuffRequestParams, UpdateApprovalStuffRequestPayload, WpDto, WpParams } from '@/types/wpTypes'
import WpService from './service'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export const WP_LIST_QUERY_KEY = 'WP_LIST_QUERY_KEY'
export const WP_STUFF_QUERY_LIST_KEY = 'WP_STUFF_QUERY_LIST_KEY'
export const WP_STUFF_REQUEST_KEY = 'WP_STUFF_REQUEST_KEY'

export class WpQueryMethods {
  public static getWpList = async (params: WpParams) => {
    const { data } = await WpService.getWpList(params)
    return data
  }

  public static getWpListToMe = async (params: WpParams) => {
    const { data } = await WpService.getWpIn(params)
    return data
  }

  public static getWpDetail = async (id: string) => {
    const { data } = await WpService.getWpDetail(id)
    return data
  }

  public static getWpLogs = async (id: string) => {
    const { data } = await WpService.getWpLogs(id)
    return data
  }

  public static getWpReports = async (params: WpDto & ListParams) => {
    const { data } = await WpService.getWpReports(params)
    return data
  }

  public static getWpReport = async (params: WpDto & { reportId: string }) => {
    const { data } = await WpService.getWpReport(params)
    return data
  }

  public static getWpStuffRequest = async (id: string) => {
    const { data } = await WpService.getStuffReq(id)
    return data
  }

  public static getStuffRequests = async (params: StuffRequestParams) => {
    const { data } = await WpService.getStuffReqList(params)
    return data
  }

  public static getStuffReqToMe = async (params: StuffRequestParams) => {
    const { data } = await WpService.getStuffReqToMe(params)
    return data
  }

  public static getCountApprovals = async (type: 'TAKE' | 'RETURN'): Promise<ApprovalsCountType> => {
    const { data } = await WpService.getCountApprovals(type)
    return data
  }
}
