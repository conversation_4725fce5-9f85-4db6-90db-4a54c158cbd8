import { ApiResponse } from '@/types/api'
import { CodeDto, CodePayload, CodeType } from '@/types/codes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import RnMService from './service'
import { FrType } from '@/types/frTypes'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'
import { WorkOrderType, WorkSegmentDtoType, WoSegmentType } from '@/types/woTypes'
import { CreateWorkOrderDtoType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { SrRmOPayload, SrType } from '@/types/srTypes'

export const useAddJobCode = (): UseMutationResult<ApiResponse<CodeType[]>, Error, CodePayload, void> => {
  return useMutation({
    mutationFn: (payload: CodePayload) => RnMService.createCode(payload)
  })
}

export const useEditJobCode = (): UseMutationResult<ApiResponse<CodeType>, Error, CodeDto & { id: string }, void> => {
  return useMutation({
    mutationFn: (payload: CodeDto & { id: string }) => RnMService.patchCode(payload.id, payload)
  })
}

export const useRemoveJobCode = (): UseMutationResult<any, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => RnMService.removeCode(id)
  })
}

export const useCreateFr = (): UseMutationResult<ApiResponse<FrType>, Error, FrDtoType, void> => {
  return useMutation({
    mutationFn: (payload: FrDtoType) => RnMService.createFr(payload)
  })
}

export const useCloseFieldReport = (): UseMutationResult<
  ApiResponse<FrType>,
  Error,
  { frId: string; closeReason: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: { frId: string; closeReason: string }) => RnMService.closeFieldReport(payload)
  })
}

export const useCreateWorkOrder = (): UseMutationResult<
  ApiResponse<WorkOrderType>,
  Error,
  CreateWorkOrderDtoType,
  void
> => {
  return useMutation({
    mutationFn: (payload: CreateWorkOrderDtoType) => RnMService.createWo(payload)
  })
}

export const useAddWoSegment = (): UseMutationResult<
  ApiResponse<WoSegmentType>,
  Error,
  WorkSegmentDtoType & { woId: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: WorkSegmentDtoType & { woId: string }) => RnMService.addWoSegment(payload)
  })
}

export const useAddWoSr = (): UseMutationResult<ApiResponse<SrType>, Error, SrRmOPayload, void> => {
  return useMutation({
    mutationFn: (payload: SrRmOPayload) => RnMService.createSr(payload)
  })
}
