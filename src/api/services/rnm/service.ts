import { CodeDto, CodeParams, CodePayload, CodeType } from '@/types/codes'
import { ApiResponse, ListResponse } from '@/types/api'
import request from '@/api/request'
import { FrLogType, FrParams, FrType } from '@/types/frTypes'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'
import { WoLogType, WoParams, WorkOrderType, WorkSegmentDtoType, WoSegmentType } from '@/types/woTypes'
import { ListParams } from '@/types/payload'
import { CreateWorkOrderDtoType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { SrRmOPayload, SrType } from '@/types/srTypes'
import { MrParams } from '@/types/mrTypes'
import { CloseFrInput } from '@/components/dialogs/close-field-report-dialog'

export const FR_LIST_QUERY_KEY = 'FR_LIST_QUERY_KEY'
export const FR_DETAIL_QUERY_KEY = 'FR_DETAIL_QUERY_KEY'
export const FR_DETAIL_LOGS_KEY = 'FR_DETAIL_LOGS_KEY'
export const WO_QUERY_LIST_KEY = 'WO_QUERY_LIST_KEY'
export const WO_QUERY_DETAIL_KEY = 'WO_QUERY_DETAIL_KEY'
export const WO_SEGMENTS_QUERY_KEY = 'WO_SEGMENTS_QUERY_KEY'
export const WO_SEGMENT_KEY = 'WO_SEGMENT_KEY'
export const WO_LOGS_QUERY_KEY = 'WO_LOGS_QUERY_KEY'

const BASE_URL = 'codes'
const FR_BASE_URL = 'field-reports'
const WO_BASE_URL = 'work-orders'
const SR_BASE_URL = 'service-requisitions'
export default class RnMService {
  public static readonly getCodeList = (params: CodeParams): Promise<ApiResponse<ListResponse<CodeType>>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly createCode = (payload: CodePayload): Promise<ApiResponse<CodeType[]>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly patchCode = (id: string, payload: CodeDto): Promise<ApiResponse<CodeType>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly removeCode = (id: string): Promise<any> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'delete'
    })
  }
  public static readonly getFrList = (params: FrParams): Promise<ApiResponse<ListResponse<FrType>>> => {
    return request({
      url: FR_BASE_URL,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getFrDetail = (frId: string): Promise<ApiResponse<FrType>> => {
    return request({
      url: `${FR_BASE_URL}/${frId}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getFrLogs = (frId: string): Promise<ApiResponse<ListResponse<FrLogType>>> => {
    return request({
      url: `${FR_BASE_URL}/${frId}/logs`,
      instance: 'CORE',
      method: 'get',
      params: { page: 1, limit: Number.MAX_SAFE_INTEGER }
    })
  }

  public static readonly createFr = (payload: FrDtoType): Promise<ApiResponse<FrType>> => {
    return request({
      url: FR_BASE_URL,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly closeFieldReport = (
    payload: CloseFrInput & { frId: string }
  ): Promise<ApiResponse<FrType>> => {
    return request({
      url: `${FR_BASE_URL}/${payload.frId}/close`,
      instance: 'CORE',
      method: 'patch',
      data: {
        closeReason: payload.closeReason
      }
    })
  }

  public static readonly getWolist = (params: WoParams): Promise<ApiResponse<ListResponse<WorkOrderType>>> => {
    return request({
      url: WO_BASE_URL,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getWoDetail = (id: string): Promise<ApiResponse<WorkOrderType>> => {
    return request({
      url: `${WO_BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getWoDetailLogs = (
    id: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<WoLogType>>> => {
    return request({
      url: `${WO_BASE_URL}/${id}/logs`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getWoSegments = (
    id: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<WoSegmentType>>> => {
    return request({
      url: `${WO_BASE_URL}/${id}/segments`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getWoSegment = (woId: string, segmentId: string): Promise<ApiResponse<WoSegmentType>> => {
    return request({
      url: `${WO_BASE_URL}/${woId}/segments/${segmentId}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly addWoSegment = (
    payload: WorkSegmentDtoType & { woId: string }
  ): Promise<ApiResponse<WoSegmentType>> => {
    return request({
      url: `${WO_BASE_URL}/${payload.woId}/segments`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly createWo = (payload: CreateWorkOrderDtoType): Promise<ApiResponse<WorkOrderType>> => {
    return request({
      url: WO_BASE_URL,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly createSr = (payload: SrRmOPayload): Promise<ApiResponse<SrType>> => {
    return request({
      url: SR_BASE_URL,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getSrList = (payload: MrParams): Promise<ApiResponse<ListResponse<SrType>>> => {
    return request({
      url: `${SR_BASE_URL}`,
      instance: 'CORE',
      method: 'get',
      params: payload
    })
  }
}
