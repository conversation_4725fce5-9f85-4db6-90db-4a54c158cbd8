import React from 'react'

import ReactDOM from 'react-dom/client'
import * as Sentry from '@sentry/react'

import App from './App'
import 'rc-time-picker/assets/index.css'
import './globals.css'
import '@/assets/iconify-icons/generated-icons.css'

Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN_URL,
  integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
  // Tracing
  tracesSampleRate: 1.0, //  Capture 100% of the transactions
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  tracePropagationTargets: ['localhost', /^https?:\/\/(?:[a-zA-Z0-9-]+\.)?equalindo360\.com(\/.*)?$/],
  // Session Replay
  replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 1.0 // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)

if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker
      .getRegistration()
      .then(reg => {
        if (!reg) {
          return navigator.serviceWorker
            .register('/sw.js', { scope: '/' })
            .then(() => console.info('[PWA] service worker registered'))
            .catch(err => console.error('[PWA] service worker registration failed', err))
        } else {
          console.info('[PWA] service worker already registered')
        }
      })
      .catch(err => console.error('[PWA] getRegistration error', err))
  })
}
