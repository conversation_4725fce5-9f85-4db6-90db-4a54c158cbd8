import { PoType } from '@/pages/purchase-order/config/types'
import { WarehouseDataType, WarehouseItemType } from './appTypes'
import { CodeNameType } from './common'
import { DepartmentType, ItemType, SiteType } from './companyTypes'
import { MrType, MrUserStatus } from './mrTypes'
import { ListParams, UserIdPayload } from './payload'
import { ApproverType, UserOutlineType } from './userTypes'

export enum MgStatus {
  RECEIVED = 'RECEIVED',
  PENDING = 'PENDING',
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED'
}

export enum ImStatus {
  APPROVED = 'APPROVED'
}

export type MgParams = {
  endDate?: string
  startDate?: string
  status?: MgStatus
} & ListParams

export type MgType = {
  id: string
  number: string
  itemsCount: number
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  isClosed: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  items: string[]
  createdByUser: UserOutlineType
  isRead?: boolean
}

export type ImPoItemType = {
  purchaseOrderItemId: number
  quantity?: number
  quantityUnit?: string
  largeUnitQuantity?: number
  excessQuantity?: number
  excessQuantityUnit?: string
  excessIsBonus?: number
  note?: string
  isActive?: boolean
  isLargeUnit?: boolean
  remainingQuantity?: number
  itemId?: string
}

export type ImPayload = {
  purchaseOrderId: string
  items: ImPoItemType[]
  deliveryNoteNumber: string
  deliveryNoteUploadId?: string
  note?: string
  siteId?: string
  shipDate?: string
  invoiceDate?: string
}

export type ImType = {
  id: string
  itemsCount: number
  note: string
  deliveryNoteNumber: string
  deliveryNoteUploadId: string
  deliveryNoteUrl: string
  deliveryNoteMimeType: string
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  journalId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  items: ImItem[]
  unbilledQuantity: number
  department: CodeNameType
  site: CodeNameType
  createdByUser: UserOutlineType
  isCrossSite?: boolean
  number?: string
  purchaseOrder?: PoType
  shipDate?: string
  invoiceDate?: string
}

export type ImParams = ListParams & {
  endDate?: string
  startDate?: string
  status?: string
  purchaseOrderId?: string
  materialRequestId?: string
  departmentId?: string
  isCrossSite?: boolean
  isBilled?: boolean
  stockMovementId?: string
  projectId?: string
  projectLabelId?: number
  isPaid?: boolean
  vendorId?: string
  isGeneralPurchase?: boolean
}

export type ImItem = {
  id: number
  incomingMaterialId: string
  purchaseOrderItemId: number
  purchaseOrderItem?: WarehouseItemType
  materialRequestItemId: any
  itemId: string
  quantity: number
  quantityUnit: string
  isLargeUnit?: boolean
  largeUnitQuantity: number
  smallQuantity: number
  remainingQuantity: number
  note: any
  createdAt: string
  updatedAt: string
  item: ItemType
  excessQuantity?: number
  excessIsBonus?: number
  unbilledQuantity?: number
  price?: number
}

export type StockLogType = {
  id: number
  type: string
  quantity: number
  quantityUnit: string
  isLargeUnit?: boolean
  largeUnitQuantity: number
  smallQuantity: number
  description: string
  itemId: string
  siteId: string
  userId: string
  approvedBy: UserOutlineType
  createdAt: string
  site: SiteType
  user: UserOutlineType
  approvedByuser: UserOutlineType
}

export type MgOutPayload = {
  materialRequestId: string
  items: MgOutItemPayload[]
  approvals: UserIdPayload[]
  note: string
}

export type MgOutItemPayload = {
  materialRequestItemId?: number
  receipts?: MgOutItemReceiptPayload[]
}

export type MgOutItemReceiptPayload = {
  materialRequestItemReceiptId?: number
  quantity?: number
  quantityUnit?: string
  largeUnitQuantity?: number
  note?: string
  isActive?: boolean
}

export type MgOutType = {
  id: string
  itemsCount: number
  approvalsCount: number
  note: any
  number: string
  cancelationNote: any
  status: string
  materialRequestId: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  journalId: string
  createdBy: string
  createdAt: string
  updatedAt: string
  materialRequest: MrType
  items: ImItem[]
  department: DepartmentType
  site: SiteType
  createdByUser: UserOutlineType
  approvals: ApproverType[]
  takenBy?: string
  takenAt?: string
  takenImageUrl?: string
} & WarehouseDataType

export type MgApprovalPayload = {
  mgId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: MrUserStatus
  isRead?: boolean
  takenBy?: string
  takenImageUploadId?: string
}

export type StockType = {
  id: number
  itemId: string
  siteId: string
  stock: number
  note?: string
  site?: SiteType
  totalStock?: number
  isMaster?: boolean
  inDocType?: string
  inDocId?: string
  inDocNumber?: string
  outDocType?: string
  outDocId?: string
  outDocNumber?: string
  createdAt?: string // ISO date-time string
  updatedAt?: string // ISO date-time string
}
