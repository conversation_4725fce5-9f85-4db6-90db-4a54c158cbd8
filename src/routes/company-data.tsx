import { CategoryContextProvider } from '@/pages/company-data/category/context/CategoryContext'
import { DepartmentContextProvider } from '@/pages/company-data/department/context/DepartmentContext'
import { GoodsItemContextProvider } from '@/pages/company-data/goods-item/context/GoodsItemContext'
import { SiteContextProvider } from '@/pages/company-data/site/context/SiteContext'
import { UnitContextProvider } from '@/pages/company-data/unit/context/UnitContext'
import { UnitContextProvider as AssetUnitProvider } from '@/pages/asset-management/units/context/UnitContext'
import { VendorContextProvider } from '@/pages/company-data/vendor/context/VendorContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'
import { AssetContextProvider } from '@/pages/asset-management/assets/context/AssetContext'
import { FormUnitContextProvider } from '@/pages/company-data/unit/context/FormUnitContext'
import { DocumentContextProvider } from '@/pages/asset-management/documents/context/DocumentContext'
import { ProjectContextProvider } from '@/pages/company-data/projects/context/ProjectContext'
import { DivisionContextProvider } from '@/pages/company-data/division/context/DivisionContext'
import { TitleContextProvider } from '@/pages/company-data/titles/context/TitleContext'
import { DraftContextProvider } from '@/pages/draft/context/DraftContext'

const GoodsItemListPage = retryDynamicImport(() => import('@/pages/company-data/goods-item'))
const GoodsItemDetailPage = retryDynamicImport(() => import('@/pages/company-data/goods-item/detail'))
const VendorListPage = retryDynamicImport(() => import('@/pages/company-data/vendor'))
const VendorDetailPage = retryDynamicImport(() => import('@/pages/company-data/vendor/detail'))
const UnitListPage = retryDynamicImport(() => import('@/pages/company-data/unit'))
const UnitDetailPage = retryDynamicImport(() => import('@/pages/company-data/unit/detail'))
const CreateEditPage = retryDynamicImport(() => import('@/pages/company-data/unit/edit-create'))
const DraftListPage = retryDynamicImport(() => import('@/pages/draft'))
const SiteListPage = retryDynamicImport(() => import('@/pages/company-data/site'))
const SiteDetailPage = retryDynamicImport(() => import('@/pages/company-data/site/detail'))
const DepartmentListPage = retryDynamicImport(() => import('@/pages/company-data/department'))
const ItemCategoryListPage = retryDynamicImport(() => import('@/pages/company-data/category/item-category'))
const ItemGroupCategoryListPage = retryDynamicImport(() => import('@/pages/company-data/category/item-group-category'))
const ItemCategoryDetailPage = retryDynamicImport(() => import('@/pages/company-data/category/item-category/detail'))
const UnitCategoryListPage = retryDynamicImport(() => import('@/pages/company-data/category/unit-category'))
const UnitCategoryDetailPage = retryDynamicImport(() => import('@/pages/company-data/category/unit-category/detail'))
const VendorCategoryListPage = retryDynamicImport(() => import('@/pages/company-data/category/vendor-category'))
const DocumentListPage = retryDynamicImport(() => import('@/pages/asset-management/documents/index'))
const InsuranceListPage = retryDynamicImport(() => import('@/pages/asset-management/documents/insurance/index'))
const ProjectList = retryDynamicImport(() => import('@/pages/company-data/projects/index'))
const ProjectDetailPage = retryDynamicImport(() => import('@/pages/company-data/projects/detail'))
const DivisionListPage = retryDynamicImport(() => import('@/pages/company-data/division/index'))
const TitleListPage = retryDynamicImport(() => import('@/pages/company-data/titles/index'))

export const companyDataRoutes = [
  {
    path: '/company-data',
    children: [
      {
        path: 'titles',
        element: (
          <TitleContextProvider>
            <Outlet />
          </TitleContextProvider>
        ),
        children: [
          {
            element: <TitleListPage />,
            index: true
          }
        ]
      },
      {
        path: 'division',
        element: (
          <DivisionContextProvider>
            <Outlet />
          </DivisionContextProvider>
        ),
        children: [
          {
            index: true,
            element: <DivisionListPage />
          }
        ]
      },
      {
        path: 'projects',
        element: (
          <ProjectContextProvider>
            <Outlet />
          </ProjectContextProvider>
        ),
        children: [
          {
            index: true,
            element: <ProjectList />
          },
          {
            path: ':projectId',
            element: <ProjectDetailPage />
          }
        ]
      },
      {
        path: 'group-category',
        children: [
          {
            path: 'item',
            element: (
              <CategoryContextProvider type='ITEM_GROUP'>
                <Outlet />
              </CategoryContextProvider>
            ),
            children: [{ element: <ItemGroupCategoryListPage />, index: true }]
          }
        ]
      },
      {
        path: 'category',
        children: [
          {
            path: 'item',
            element: (
              <CategoryContextProvider type='ITEM'>
                <Outlet />
              </CategoryContextProvider>
            ),
            children: [
              { element: <ItemCategoryListPage />, index: true },
              { path: ':catId', element: <ItemCategoryDetailPage /> }
            ]
          },
          {
            path: 'unit',
            element: (
              <CategoryContextProvider type='UNIT'>
                <Outlet />
              </CategoryContextProvider>
            ),
            children: [
              { element: <UnitCategoryListPage />, index: true },
              {
                path: ':categoryId',
                children: [
                  {
                    element: <UnitCategoryDetailPage />,
                    index: true
                  }
                ]
              }
            ]
          },
          {
            path: 'vendor',
            element: (
              <CategoryContextProvider type='VENDOR'>
                <Outlet />
              </CategoryContextProvider>
            ),
            children: [{ element: <VendorCategoryListPage />, index: true }]
          }
        ]
      },
      {
        path: 'item',
        element: (
          <GoodsItemContextProvider>
            <Outlet />
          </GoodsItemContextProvider>
        ),
        children: [
          {
            element: <GoodsItemListPage />,
            index: true
          },
          {
            path: ':itemId',
            element: <GoodsItemDetailPage />
          }
        ]
      },
      {
        path: 'vendor',
        element: (
          <VendorContextProvider>
            <Outlet />
          </VendorContextProvider>
        ),
        children: [
          {
            element: <VendorListPage />,
            index: true
          },
          {
            path: ':vendorId',
            element: <VendorDetailPage />
          }
        ]
      },
      {
        path: 'unit',
        element: (
          <UnitContextProvider>
            <Outlet />
          </UnitContextProvider>
        ),
        children: [
          {
            element: <UnitListPage />,
            index: true
          },
          {
            path: ':unitId',
            element: <UnitDetailPage />
          }
        ]
      },
      {
        path: 'site',
        element: (
          <SiteContextProvider>
            <Outlet />
          </SiteContextProvider>
        ),
        children: [
          {
            element: <SiteListPage />,
            index: true
          },
          {
            path: ':unitId',
            element: <SiteDetailPage />
          }
        ]
      },
      {
        path: 'department',
        element: (
          <DepartmentContextProvider>
            <DepartmentListPage />
          </DepartmentContextProvider>
        )
      },
      {
        path: 'assets',
        element: <Outlet />,
        children: [
          {
            path: 'goods',
            element: (
              <GoodsItemContextProvider>
                <Outlet />
              </GoodsItemContextProvider>
            ),
            children: [
              {
                element: <GoodsItemListPage />,
                index: true
              },
              {
                path: ':itemId',
                element: <GoodsItemDetailPage />
              }
            ]
          },
          {
            path: 'unit',
            element: (
              <UnitContextProvider>
                <AssetUnitProvider>
                  <Outlet />
                </AssetUnitProvider>
              </UnitContextProvider>
            ),
            children: [
              {
                path: 'list',
                element: <UnitListPage />
              },
              {
                path: 'list/:unitId',
                element: <UnitDetailPage />
              },
              {
                path: 'draft',
                element: (
                  <DraftContextProvider>
                    <DraftListPage />
                  </DraftContextProvider>
                )
              },
              {
                path: ':unitId/:mode',
                element: (
                  <DraftContextProvider>
                    <FormUnitContextProvider>
                      <CreateEditPage />
                    </FormUnitContextProvider>
                  </DraftContextProvider>
                )
              }
            ]
          },
          {
            path: 'document',
            element: (
              <DocumentContextProvider>
                <Outlet />
              </DocumentContextProvider>
            ),
            children: [
              {
                element: <DocumentListPage />,
                index: true
              }
            ]
          },
          {
            path: 'insurance',
            element: (
              <DocumentContextProvider>
                <Outlet />
              </DocumentContextProvider>
            ),
            children: [
              {
                element: <InsuranceListPage alternativeTitle='List Asuransi' />,
                index: true
              }
            ]
          }
        ]
      }
    ]
  }
] as RouteObject[]
